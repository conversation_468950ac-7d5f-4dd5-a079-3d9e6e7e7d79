"""对话处理模块

包含对话标注、语句块合并等相关功能
"""

import json
from datetime import datetime
from typing import Dict, List

from ...utils.logging_config import get_logger
from ..langchain_config import TaskType

logger = get_logger(__name__)

from .langsmith_config import log_workflow_metrics, with_langsmith_tracing  # noqa: E402
from .novel_prompts import NovelProcessingPrompts  # noqa: E402


class DialogueProcessor:
    """对话处理器"""

    def __init__(self, workflow_instance):
        """
        初始化对话处理器

        Args:
            workflow_instance: 主工作流实例，用于访问共享方法
        """
        self.workflow = workflow_instance

    @property
    def llm_client(self):
        """延迟访问llm_client"""
        return self.workflow.llm_client

    @with_langsmith_tracing(
        run_name="annotate_dialogues",
        tags=["dialogue_annotation", "llm_call", "batch_processing"],
        exclude_fields=[
            "novel_content",
            "chapter_contents",
            "overall_analysis",
            "characters",
            "chapters",
        ],
    )
    def annotate_dialogues(self, state):
        """对话标注节点"""
        logger.info(f"{state['workflow_id']}: 开始对话标注")

        # 记录对话标注开始指标
        log_workflow_metrics(
            state,
            "annotate_dialogues",
            {
                "chapters_to_process": len(state["chapters"]),
                "characters_count": len(state["characters"]),
            },
        )

        try:
            chapters = state["chapters"]
            characters = state["characters"]
            chapter_contents = state["chapter_contents"]  # 使用原始章节内容
            annotated_chapters = []

            # 确保章节数量匹配
            if len(chapters) != len(chapter_contents):
                logger.warning(
                    f"{state['workflow_id']}: 章节分析结果数量({len(chapters)}) "
                    f"与原始章节内容数量({len(chapter_contents)})不匹配"
                )

            # 逐章节处理
            for i, chapter in enumerate(chapters):
                logger.info(
                    f"{state['workflow_id']}: 处理第 {i + 1}/{len(chapters)} 章"
                )

                # 获取对应的原始章节内容
                if i < len(chapter_contents):
                    actual_chapter_content = chapter_contents[i]
                else:
                    # 如果没有对应的原始内容，使用章节分析结果中的content字段作为备选
                    actual_chapter_content = chapter.get("content", "")
                    logger.warning(
                        f"{state['workflow_id']}: 第{i + 1}章使用章节分析结果中的content字段作为备选"
                    )

                # 过滤角色信息，只保留对话标注需要的字段
                filtered_characters = [
                    {
                        "name": char.get("name", ""),
                        "type": char.get("type", ""),
                        "aliases": char.get("aliases", []),
                    }
                    for char in characters
                ]

                # 使用专业对话标注提示词模板
                characters_info = json.dumps(
                    filtered_characters, ensure_ascii=False, indent=2
                )
                messages = NovelProcessingPrompts.format_professional_dialogue_annotation_messages(
                    characters_info=characters_info,
                    chapter_index=str(i),
                    chapter_title=chapter["title"],
                    chapter_content=actual_chapter_content,  # 使用原始章节内容
                )

                # 调用LLM
                result = self.llm_client.call_with_fallback(
                    TaskType.DIALOGUE_ANNOTATION, messages
                )

                if not result["success"]:
                    raise Exception(f"第{i + 1}章对话标注失败: {result.get('error')}")

                # 解析和验证 JSON 格式的对话标注结果
                try:
                    # 尝试解析 JSON
                    dialogue_annotations = json.loads(result["content"].strip())

                    # 验证格式
                    if not isinstance(dialogue_annotations, list):
                        raise ValueError("对话标注结果必须是数组格式")

                    # 验证每个标注项的必需字段
                    required_fields = ["type", "speaker", "emotion", "content"]
                    for idx, annotation in enumerate(dialogue_annotations):
                        if not isinstance(annotation, dict):
                            raise ValueError(f"第{idx + 1}个标注项必须是对象格式")

                        for field in required_fields:
                            if field not in annotation:
                                raise ValueError(
                                    f"第{idx + 1}个标注项缺少必需字段: {field}"
                                )

                    logger.info(
                        f"{state['workflow_id']}: 第{i + 1}章解析到 {len(dialogue_annotations)} 个对话标注"
                    )

                except json.JSONDecodeError as e:
                    logger.error(
                        f"{state['workflow_id']}: 第{i + 1}章 JSON 解析失败: {e}"
                    )
                    # 保存原始内容以便调试
                    dialogue_annotations = None

                except ValueError as e:
                    logger.error(
                        f"{state['workflow_id']}: 第{i + 1}章标注格式验证失败: {e}"
                    )
                    dialogue_annotations = None

                # 保存标注结果，同时添加实际使用的章节内容
                annotated_chapter = {
                    **chapter,
                    "content": actual_chapter_content,  # 确保包含实际使用的章节内容
                    "dialogue_annotations": dialogue_annotations,  # 解析后的结构化数据
                    "annotation_timestamp": datetime.now().isoformat(),
                    "annotation_status": "success"
                    if dialogue_annotations is not None
                    else "failed",
                }
                annotated_chapters.append(annotated_chapter)

                # 更新进度
                progress = 0.5 + (0.2 * (i + 1) / len(chapters))
                state = {
                    **state,
                    "current_chapter": i + 1,
                    "processing_progress": progress,
                    "updated_at": datetime.now().isoformat(),
                }

            logger.info(
                f"{state['workflow_id']}: 完成 {len(chapters)} 个章节的对话标注"
            )

            return {
                **state,
                "step": "merge_continuous_segments",
                "annotated_chapters": annotated_chapters,
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 0.7,
            }

        except Exception as e:
            return self.workflow.handle_error(state, e)

    @with_langsmith_tracing(
        run_name="merge_continuous_segments",
        tags=["segment_merging"],
        exclude_fields=[
            "novel_content",
            "chapter_contents",
            "overall_analysis",
            "characters",
            "chapters",
        ],
    )
    def merge_continuous_segments(self, state):
        """合并连续的相同属性语句块"""
        logger.info(f"{state['workflow_id']}: 开始合并连续的相同属性语句块")

        # 记录合并开始指标
        log_workflow_metrics(
            state,
            "merge_continuous_segments",
            {
                "chapters_to_process": len(state["annotated_chapters"]),
            },
        )

        try:
            annotated_chapters = state["annotated_chapters"]

            # 逐章节处理，直接修改原有章节
            for i, chapter in enumerate(annotated_chapters):
                logger.info(
                    f"{state['workflow_id']}: 处理第 {i + 1}/{len(annotated_chapters)} 章"
                )

                # 获取原始标注
                original_annotations = chapter.get("dialogue_annotations", [])

                # 合并连续的相同属性语句块
                merged_annotations = (
                    self.merge_segments(original_annotations)
                    if original_annotations
                    else []
                )

                # 直接修改章节的标注数据和统计信息
                chapter["dialogue_annotations"] = merged_annotations
                chapter["merge_stats"] = {
                    "original_count": len(original_annotations),
                    "merged_count": len(merged_annotations),
                    "reduction_rate": (
                        len(original_annotations) - len(merged_annotations)
                    )
                    / len(original_annotations)
                    if original_annotations
                    else 0,
                }

                # 更新进度
                progress = 0.6 + (0.3 * (i + 1) / len(annotated_chapters))
                state = {
                    **state,
                    "current_chapter": i + 1,
                    "processing_progress": progress,
                    "updated_at": datetime.now().isoformat(),
                }

            logger.info(
                f"{state['workflow_id']}: 完成 {len(annotated_chapters)} 个章节的连续语句块合并"
            )

            return {
                **state,
                "step": "post_process",
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 0.8,
            }

        except Exception as e:
            return self.workflow.handle_error(state, e)

    def merge_segments(self, segments: List[Dict]) -> List[Dict]:
        """
        合并连续的相同属性语句块

        Args:
            segments: 原始的对话标注列表

        Returns:
            合并后的对话标注列表
        """
        if not segments:
            return []

        merged_segments = []
        current_group = [segments[0]]

        for segment in segments[1:]:
            # 检查是否可以合并：type、speaker、emotion都相同
            if (
                segment["type"] == current_group[0]["type"]
                and segment["speaker"] == current_group[0]["speaker"]
                and segment["emotion"] == current_group[0]["emotion"]
            ):
                # 添加到当前组
                current_group.append(segment)
            else:
                # 不能合并，处理当前组并开始新组
                merged_segments.append(self.merge_group(current_group))
                current_group = [segment]

        # 处理最后一组
        merged_segments.append(self.merge_group(current_group))

        return merged_segments

    def merge_group(self, group: List[Dict]) -> Dict:
        """
        合并一组相同属性的语句块

        Args:
            group: 相同属性的语句块列表

        Returns:
            合并后的语句块
        """
        if len(group) == 1:
            return group[0]

        # 合并内容，智能处理分隔符
        merged_content = group[0]["content"]

        for segment in group[1:]:
            content_to_add = segment["content"]

            # 智能分隔符逻辑
            separator = self._get_smart_separator(
                merged_content, content_to_add, segment["type"]
            )

            merged_content += separator + content_to_add

        # 返回合并后的语句块
        return {
            "type": group[0]["type"],
            "speaker": group[0]["speaker"],
            "emotion": group[0]["emotion"],
            "content": merged_content.strip(),
            "merged_count": len(group),  # 记录合并了多少个原始块
        }

    def _get_smart_separator(
        self, prev_content: str, next_content: str, content_type: str
    ) -> str:
        """
        智能判断两个内容片段之间应该使用什么分隔符

        Args:
            prev_content: 前一段内容
            next_content: 后一段内容
            content_type: 内容类型 (dialogue/narration/其他)

        Returns:
            合适的分隔符
        """
        if not prev_content or not next_content:
            return ""

        # 获取前一段的最后一个字符和后一段的第一个字符
        prev_last = prev_content.rstrip()[-1] if prev_content.rstrip() else ""
        next_first = next_content.lstrip()[0] if next_content.lstrip() else ""

        # 定义标点符号集合
        chinese_punctuation = {
            "。",
            "，",
            "；",
            "：",
            "！",
            "？",
            "…",
            "、",
            "》",
            "』",
            "」",
            "〉",
            "）",
            "］",
            "｝",
        }
        english_punctuation = {".", ",", ";", ":", "!", "?", ")", "]", "}", '"', "'"}
        opening_punctuation = {
            "《",
            "『",
            "「",
            "〈",
            "（",
            "［",
            "｛",
            '"',
            "'",
            "(",
            "[",
            "{",
        }

        # 如果前一段以标点符号结尾
        if prev_last in chinese_punctuation or prev_last in english_punctuation:
            # 对话类型：通常标点后直接连接
            if content_type == "dialogue":
                return ""
            # 叙述类型：标点后可能需要空格，除非后面是开头标点
            elif content_type == "narration":
                if next_first in opening_punctuation:
                    return ""
                else:
                    return " "
            else:
                return " "

        # 如果前一段以空格结尾，不需要额外分隔符
        if prev_last == " ":
            return ""

        # 如果后一段以开头标点开始，通常不需要空格
        if next_first in opening_punctuation:
            return ""

        # 如果后一段以标点开始，不需要空格
        if next_first in chinese_punctuation or next_first in english_punctuation:
            return ""

        # 对话类型的特殊处理
        if content_type == "dialogue":
            # 对话内容通常更紧密，减少不必要的空格
            # 如果是中文对话，通常直接连接
            if self._is_chinese_char(prev_last) and self._is_chinese_char(next_first):
                return ""
            # 如果是英文对话，需要空格
            elif self._is_english_char(prev_last) or self._is_english_char(next_first):
                return " "
            # 其他情况直接连接
            else:
                return ""

        # 叙述类型：更保守，通常加空格
        elif content_type == "narration":
            return " "

        # 默认情况：根据字符类型决定
        if (self._is_chinese_char(prev_last) and self._is_chinese_char(next_first)) or (
            self._is_chinese_char(prev_last) and next_first in chinese_punctuation
        ):
            return ""
        else:
            return " "

    def _is_chinese_char(self, char: str) -> bool:
        """判断是否为中文字符"""
        if not char:
            return False
        return "\u4e00" <= char <= "\u9fff"

    def _is_english_char(self, char: str) -> bool:
        """判断是否为英文字母"""
        if not char:
            return False
        return char.isalpha() and ord(char) < 128
