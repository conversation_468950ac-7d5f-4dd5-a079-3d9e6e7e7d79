"""
LLM客户端 - 使用liteLLM统一调用不同的大模型API
"""

import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

try:
    from ..utils.logging_config import get_logger
except ImportError:
    # 备用导入方案
    import sys
    from pathlib import Path

    if str(Path(__file__).parent.parent) not in sys.path:
        sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils.logging_config import get_logger

logger = get_logger(__name__)

try:
    import litellm
    from litellm import acompletion, completion
    from litellm.caching import Cache

    # 在模块加载时立即设置，确保在任何操作前都生效
    litellm.set_verbose = False
    # 强制禁用所有 LiteLLM 调试输出
    import logging

    logging.getLogger("litellm").setLevel(logging.WARNING)
    # 禁用 LiteLLM 的其他调试模式
    if hasattr(litellm, "_logging"):
        litellm._logging = False
    if hasattr(litellm, "verbose"):
        litellm.verbose = False

except ImportError:
    logger.warning("litellm未安装，部分功能可能受限。请运行: pip install litellm")
    litellm = None
    completion = None
    acompletion = None
    Cache = None

from .config import LLMConfig, ModelProvider, TaskType


class CostTracker:
    """成本追踪器"""

    def __init__(self):
        self.daily_costs: Dict[str, float] = {}
        self.model_usage: Dict[str, int] = {}
        self.last_reset = datetime.now().date()

    def add_cost(
        self,
        model_name: str,
        input_tokens: int,
        output_tokens: int,
        input_cost_per_1k: float,
        output_cost_per_1k: float,
    ):
        """添加成本记录"""
        self._check_daily_reset()

        input_cost = (input_tokens / 1000) * input_cost_per_1k
        output_cost = (output_tokens / 1000) * output_cost_per_1k
        total_cost = input_cost + output_cost

        today = str(datetime.now().date())

        self.daily_costs[today] = self.daily_costs.get(today, 0) + total_cost
        self.model_usage[model_name] = (
            self.model_usage.get(model_name, 0) + input_tokens + output_tokens
        )

        logger.info(
            f"模型 {model_name} 使用 {input_tokens} 输入tokens + {output_tokens} 输出tokens，"
            f"成本 ¥{total_cost:.4f} (输入: ¥{input_cost:.4f}, 输出: ¥{output_cost:.4f})"
        )

    def get_daily_cost(self) -> float:
        """获取今日成本"""
        today = str(datetime.now().date())
        return self.daily_costs.get(today, 0)

    def get_model_usage(self) -> Dict[str, int]:
        """获取模型使用统计"""
        return self.model_usage.copy()

    def _check_daily_reset(self):
        """检查是否需要重置日统计"""
        today = datetime.now().date()
        if today > self.last_reset:
            self.daily_costs.clear()
            self.last_reset = today


class LLMClient:
    """LLM统一客户端"""

    def __init__(self, config: Optional[LLMConfig] = None):
        self.config = config or LLMConfig()
        self.cost_tracker = CostTracker()
        self._setup_litellm()

    def _setup_litellm(self):
        """设置liteLLM"""
        if litellm is None:
            raise ImportError("litellm未安装，请运行: pip install litellm")

        # litellm.set_verbose = False # 已移动到模块顶部

        # 全局设置，自动删除模型不支持的参数
        litellm.drop_params = True  # 自动删除不支持的参数
        litellm.modify_params = True  # 自动修改参数格式

        # 抑制token counter错误
        litellm.suppress_debug_info = True
        if hasattr(litellm, "token_counter"):
            litellm.token_counter.suppress_errors = True

        # 禁用max_tokens检查，避免未知模型错误
        litellm.disable_max_tokens_check = True

        # 尝试禁用token计数器
        try:
            import litellm.litellm_core_utils.token_counter as token_counter
            # 猴子补丁：让get_modified_max_tokens总是返回None
            original_get_modified_max_tokens = token_counter.get_modified_max_tokens
            def patched_get_modified_max_tokens(*args, **kwargs):
                try:
                    return original_get_modified_max_tokens(*args, **kwargs)
                except Exception:
                    # 如果出错，返回None让LiteLLM使用默认值
                    return None
            token_counter.get_modified_max_tokens = patched_get_modified_max_tokens
            logger.debug("已应用token counter补丁")
        except Exception as e:
            logger.debug(f"无法应用token counter补丁: {e}")

        # 抑制Pydantic序列化警告（根本解决方案）
        self._suppress_litellm_warnings()

        # 添加自定义模型配置，避免未知模型错误
        self._register_custom_models()

    def _suppress_litellm_warnings(self):
        """抑制LiteLLM的Pydantic序列化警告和其他输出"""
        import logging
        import os
        import warnings

        # 方法1: 抑制特定的Pydantic警告
        warnings.filterwarnings(
            "ignore", message=".*Pydantic serializer warnings.*", category=UserWarning
        )

        # 方法2: 设置环境变量抑制警告
        os.environ.setdefault("PYTHONWARNINGS", "ignore::UserWarning")

        # 方法3: 针对LiteLLM的特定警告抑制
        try:
            # 如果有pydantic可用，禁用序列化警告
            import pydantic

            if hasattr(pydantic, "warnings"):
                pydantic.warnings.warn = lambda *args, **kwargs: None
        except ImportError:
            pass

        # 方法4: 抑制LiteLLM内部日志输出
        try:
            # 设置LiteLLM相关日志级别 - 强制设置为WARNING以完全抑制DEBUG
            logging.getLogger("litellm").setLevel(logging.WARNING)
            logging.getLogger("httpx").setLevel(logging.WARNING)
            logging.getLogger("httpcore").setLevel(logging.WARNING)

            # 抑制LiteLLM的stdout输出
            if litellm:
                litellm.suppress_debug_info = True
                # 如果有verbose设置，确保关闭
                if hasattr(litellm, "verbose"):
                    litellm.verbose = False
                # 额外的调试模式禁用
                if hasattr(litellm, "_logging"):
                    litellm._logging = False
                if hasattr(litellm, "debug"):
                    litellm.debug = False
        except Exception as e:
            logger.debug(f"设置LiteLLM日志抑制时出现异常: {e}")

        # 方法5: 抑制Provider List等输出
        try:
            # 重定向LiteLLM可能的print输出
            pass

            # 保存原始stdout，但不在这里重定向（可能影响其他输出）
            # 这部分在实际调用时处理
        except Exception:
            pass

        logger.info("已设置LiteLLM警告和输出抑制")

    def _call_with_suppressed_output(self, func):
        """在抑制stdout输出的情况下调用函数"""
        import sys
        from io import StringIO

        # 保存原始stdout
        original_stdout = sys.stdout

        try:
            # 临时重定向stdout到空设备
            sys.stdout = StringIO()

            # 执行函数
            result = func()

            return result

        except Exception as e:
            # 确保在异常情况下也恢复stdout
            raise e
        finally:
            # 恢复原始stdout
            sys.stdout = original_stdout

    def _register_custom_models(self):
        """注册自定义模型配置，避免 LiteLLM 未知模型错误"""
        try:
            # 为新模型添加配置，避免 token counter 错误
            custom_models = {
                # Google AI Studio Gemini 模型 - 使用正确的格式
                "gemini-2.0-flash-exp": {
                    "max_tokens": 8192,
                    "input_cost_per_token": 0.0,  # 免费模型
                    "output_cost_per_token": 0.0,  # 免费模型
                    "litellm_provider": "gemini",
                    "mode": "chat",
                },
                "gemini-1.5-flash": {
                    "max_tokens": 8192,
                    "input_cost_per_token": 0.0,  # 免费模型
                    "output_cost_per_token": 0.0,  # 免费模型
                    "litellm_provider": "gemini",
                    "mode": "chat",
                },
                "gemini-1.5-flash-8b": {
                    "max_tokens": 8192,
                    "input_cost_per_token": 0.0,  # 免费模型
                    "output_cost_per_token": 0.0,  # 免费模型
                    "litellm_provider": "gemini",
                    "mode": "chat",
                },
                "openrouter/deepseek/deepseek-chat-v3-0324:free": {
                    "max_tokens": 128000,
                    "input_cost_per_token": 0.0,  # 免费模型
                    "output_cost_per_token": 0.0,  # 免费模型
                    "litellm_provider": "openrouter",
                    "mode": "chat",
                },
                "openrouter/deepseek/deepseek-chat-v3-0324": {
                    "max_tokens": 164000,
                    "input_cost_per_token": 0.0000003,  # $0.30/M tokens
                    "output_cost_per_token": 0.00000088,  # $0.88/M tokens
                    "litellm_provider": "openrouter",
                    "mode": "chat",
                },
                "openrouter/qwen/qwen3-235b-a22b:free": {
                    "max_tokens": 40960,
                    "input_cost_per_token": 0.0,  # 免费模型
                    "output_cost_per_token": 0.0,  # 免费模型
                    "litellm_provider": "openrouter",
                    "mode": "chat",
                },
                "openrouter/qwen/qwen3-235b-a22b": {
                    "max_tokens": 41000,
                    "input_cost_per_token": 0.00000013,  # $0.13/M tokens
                    "output_cost_per_token": 0.0000006,  # $0.60/M tokens
                    "litellm_provider": "openrouter",
                    "mode": "chat",
                },

            }

            # 使用 litellm.register_model() 替代直接修改 model_cost
            if hasattr(litellm, "register_model"):
                litellm.register_model(custom_models)
                logger.info(
                    f"已通过 register_model 注册 {len(custom_models)} 个自定义模型。"
                )
            elif hasattr(litellm, "model_cost"):
                # 旧版 litellm 的回退方案
                for model_name, config in custom_models.items():
                    if model_name not in litellm.model_cost:
                        litellm.model_cost[model_name] = config
                        logger.info(f"已注册自定义模型配置: {model_name}")

            # 注意：不要设置litellm.model_fallbacks，这会与LiteLLM的fallbacks参数冲突

            # 直接添加到model_cost字典，强制覆盖
            if hasattr(litellm, "model_cost"):
                for model_name, config in custom_models.items():
                    litellm.model_cost[model_name] = config
                    logger.debug(f"强制注册模型: {model_name}")

            # 注意：不要设置model_alias_map，可能与LiteLLM内部机制冲突

        except Exception as e:
            logger.warning(f"注册自定义模型配置时出错: {e}")

    def _prepare_model_call(self, model_name: str) -> Dict[str, Any]:
        """准备模型调用参数"""
        model_config = self.config.get_model_config(model_name)
        if not model_config:
            raise ValueError(f"未找到模型配置: {model_name}")

        # 获取API密钥
        api_key = os.getenv(model_config.api_key_env)
        if not api_key:
            raise ValueError(f"未找到API密钥环境变量: {model_config.api_key_env}")

        # 准备调用参数
        call_params = {
            "model": model_config.name,
            "api_key": api_key,
            "max_tokens": model_config.max_tokens,
            "temperature": model_config.temperature,
            "timeout": model_config.timeout,
        }

        # 对于未知模型，强制设置max_tokens避免token counter错误
        if "deepseek" in model_config.name or "qwen" in model_config.name:
            call_params["max_tokens"] = min(model_config.max_tokens, 4096)  # 使用保守的值

        # 添加base_url（如果需要）
        if model_config.base_url:
            call_params["api_base"] = model_config.base_url

        # 明确指定提供商（修复LiteLLM识别问题）
        if model_config.provider == ModelProvider.OPENROUTER:
            call_params["custom_llm_provider"] = "openrouter"
        elif model_config.api_key_env == "GEMINI_API_KEY":
            # Google AI Studio的Gemini模型
            call_params["custom_llm_provider"] = "gemini"
            # 确保使用正确的API密钥环境变量名
            call_params["api_key"] = api_key
            # 不设置api_base，让LiteLLM使用默认的Google AI Studio端点



        return call_params, model_config

    def call_model(
        self, model_name: str, messages: List[Dict[str, str]], **kwargs
    ) -> Dict[str, Any]:
        """调用指定模型"""
        try:
            call_params, model_config = self._prepare_model_call(model_name)

            # 合并额外参数
            call_params.update(kwargs)

            # 对于问题模型，禁用token计数
            if "deepseek" in model_config.name or "qwen" in model_config.name:
                call_params["disable_token_counting"] = True

            # 对于Google AI Studio模型，确保正确的provider设置
            if model_config.name.startswith("gemini/") and model_config.api_key_env == "GEMINI_API_KEY":
                call_params["custom_llm_provider"] = "gemini"
                # 禁用token计数以避免模型映射问题
                call_params["disable_token_counting"] = True

            # 调用模型时抑制可能的stdout输出
            response = self._call_with_suppressed_output(
                lambda: completion(messages=messages, **call_params)
            )

            # 记录成本
            total_cost = 0
            if hasattr(response, "usage") and response.usage:
                input_tokens = getattr(response.usage, "prompt_tokens", 0)
                output_tokens = getattr(response.usage, "completion_tokens", 0)

                self.cost_tracker.add_cost(
                    model_name,
                    input_tokens,
                    output_tokens,
                    model_config.input_cost_per_1k_tokens,
                    model_config.output_cost_per_1k_tokens,
                )

                # 计算总成本
                total_cost = model_config.calculate_cost(input_tokens, output_tokens)

            # 返回标准化响应
            return {
                "success": True,
                "content": response.choices[0].message.content,
                "model": model_name,
                "usage": response.usage.model_dump()
                if hasattr(response, "usage")
                else None,
                "cost": total_cost,
            }

        except Exception as e:
            logger.error(f"模型 {model_name} 调用失败: {str(e)}")
            return {"success": False, "error": str(e), "model": model_name}

    def call_with_fallback(
        self, task_type: TaskType, messages: List[Dict[str, str]], **kwargs
    ) -> Dict[str, Any]:
        """带故障转移的模型调用"""
        task_config = self.config.get_task_config(task_type)
        if not task_config:
            raise ValueError(f"未找到任务配置: {task_type}")

        # 检查预算
        if self.cost_tracker.get_daily_cost() > self.config.cost_limits["daily_budget"]:
            logger.warning("已超出日预算限制")
            return {"success": False, "error": "已超出日预算限制"}

        # 尝试主模型和备用模型
        models_to_try = [task_config.primary_model] + task_config.fallback_models

        for model_name in models_to_try:
            logger.info(f"尝试使用模型: {model_name}")

            result = self.call_model(model_name, messages, **kwargs)

            if result["success"]:
                logger.info(f"模型 {model_name} 调用成功")
                return result
            else:
                logger.warning(f"模型 {model_name} 调用失败: {result.get('error')}")
                continue

        # 所有模型都失败
        return {
            "success": False,
            "error": "所有模型调用都失败",
            "tried_models": models_to_try,
        }

    def validate_response(self, response: str, expected_format: str = "json") -> bool:
        """验证响应格式"""
        if expected_format == "json":
            try:
                json.loads(response)
                return True
            except json.JSONDecodeError:
                return False

        # 其他格式验证可以在这里添加
        return True

    def get_cost_summary(self) -> Dict[str, Any]:
        """获取成本摘要"""
        return {
            "daily_cost": self.cost_tracker.get_daily_cost(),
            "daily_budget": self.config.cost_limits["daily_budget"],
            "remaining_budget": self.config.cost_limits["daily_budget"]
            - self.cost_tracker.get_daily_cost(),
            "model_usage": self.cost_tracker.get_model_usage(),
        }

    def test_connection(self, model_name: str) -> bool:
        """测试模型连接"""
        test_messages = [{"role": "user", "content": "Hello, this is a test message."}]

        result = self.call_model(model_name, test_messages)
        return result["success"]

    def list_available_models(self) -> List[str]:
        """列出可用的模型"""
        available_models = []

        for model_name in self.config.models.keys():
            model_config = self.config.get_model_config(model_name)
            api_key = os.getenv(model_config.api_key_env)

            if api_key:
                available_models.append(model_name)

        return available_models
