#!/usr/bin/env python3
"""
简化的 LangChain 迁移测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from src.llm.langchain_config import LangChainLLMConfig, TaskType
        print("  [OK] LangChain 配置模块")
        
        from src.llm.langchain_client import LangChainLLMClient
        print("  [OK] LangChain 客户端模块")
        
        from src.llm.langchain_manager import LangChainLLMManager
        print("  [OK] LangChain 管理器模块")
        
        from src.llm.manager import LLMManager
        print("  [OK] 主管理器模块")
        
        return True
    except Exception as e:
        print(f"  [FAIL] 导入失败: {e}")
        return False

def test_initialization():
    """测试初始化"""
    print("\n测试模块初始化...")
    
    try:
        from src.llm.langchain_config import LangChainLLMConfig
        config = LangChainLLMConfig()
        print(f"  [OK] 配置初始化成功，模型数量: {len(config.models)}")
        
        from src.llm.langchain_client import LangChainLLMClient
        client = LangChainLLMClient()
        print(f"  [OK] 客户端初始化成功")
        
        from src.llm.langchain_manager import LangChainLLMManager
        manager = LangChainLLMManager()
        print(f"  [OK] LangChain 管理器初始化成功")
        
        from src.llm.manager import LLMManager
        main_manager = LLMManager(use_langchain=True)
        print(f"  [OK] 主管理器初始化成功")
        
        return True
    except Exception as e:
        print(f"  [FAIL] 初始化失败: {e}")
        return False

def test_workflow_integration():
    """测试工作流集成"""
    print("\n测试工作流集成...")
    
    try:
        from src.llm.manager import LLMManager
        from src.llm.workflows.novel_processing import NovelProcessingWorkflow
        
        # 创建使用 LangChain 的管理器
        manager = LLMManager(use_langchain=True)
        print("  [OK] 创建 LangChain 管理器成功")
        
        # 检查工作流是否正确注册
        workflows = manager.workflows
        print(f"  [OK] 注册的工作流: {list(workflows.keys())}")
        
        # 检查工作流是否使用 LangChain
        novel_workflow = workflows.get("novel_processing")
        if novel_workflow:
            print(f"  [OK] 小说处理工作流已注册")
            print(f"  [OK] 工作流客户端类型: {type(novel_workflow.llm_client).__name__}")
        else:
            print("  [FAIL] 小说处理工作流未找到")
            return False
        
        return True
    except Exception as e:
        print(f"  [FAIL] 工作流集成测试失败: {e}")
        return False

def test_api_compatibility():
    """测试 API 兼容性"""
    print("\n测试 API 兼容性...")
    
    try:
        from src.llm.manager import LLMManager
        
        # 测试新的 LangChain 管理器
        langchain_manager = LLMManager(use_langchain=True)
        
        # 测试基本方法是否存在
        methods_to_test = [
            'get_cost_summary',
            'test_connections',
            'call_llm_directly',
            'run_workflow',
            'get_workflow_status'
        ]
        
        for method_name in methods_to_test:
            if hasattr(langchain_manager, method_name):
                print(f"  [OK] 方法 {method_name} 存在")
            else:
                print(f"  [FAIL] 方法 {method_name} 不存在")
                return False
        
        # 测试成本摘要
        cost_summary = langchain_manager.get_cost_summary()
        print(f"  [OK] 成本摘要: {cost_summary}")
        
        return True
    except Exception as e:
        print(f"  [FAIL] API 兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("LangChain 迁移简化测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("模块初始化", test_initialization),
        ("工作流集成", test_workflow_integration),
        ("API兼容性", test_api_compatibility),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "[OK] 通过" if result else "[FAIL] 失败"
        print(f"{test_name:15}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过！LangChain 迁移基础功能正常！")
        return 0
    else:
        print("[ERROR] 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
