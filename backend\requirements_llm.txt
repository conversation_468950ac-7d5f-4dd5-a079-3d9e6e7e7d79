# LLM模块依赖
# 核心依赖
langgraph>=0.2.0         # 工作流图框架
langchain-core>=0.3.0    # LangChain核心组件
langsmith>=0.1.0         # LangSmith 监控和追踪

# LangChain 提供商依赖
langchain-openai>=0.2.0  # OpenAI/OpenRouter 集成
langchain-google-genai>=2.0.0  # Google Gemini 集成

# 可选依赖（根据使用的模型提供商选择）
openai>=1.0.0           # OpenAI API客户端
google-generativeai>=0.8.0  # Google Gemini API

# 工具依赖
pydantic>=2.0.0         # 数据验证
typing-extensions>=4.0.0 # 类型扩展
python-dotenv>=1.0.0    # 环境变量管理
uvicorn
sse-starlette 