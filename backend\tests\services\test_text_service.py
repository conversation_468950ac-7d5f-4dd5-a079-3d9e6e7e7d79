#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本服务单元测试
"""

import pytest
from src.services.text_service import TextService



class TestTextService:
    """测试文本服务类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.text_service = TextService()

    def test_clean_text_basic(self):
        """测试基本文本清理功能"""
        # 测试移除BOM头
        text_with_bom = "\ufeff这是测试文本"
        cleaned = self.text_service.clean_text_basic(text_with_bom)
        assert not cleaned.startswith("\ufeff")

        # 测试统一换行符
        text_with_mixed_breaks = "第一行\r\n第二行\r第三行\n第四行"
        cleaned = self.text_service.clean_text_basic(text_with_mixed_breaks)
        assert "\r\n" not in cleaned
        assert "\r" not in cleaned

        # 测试移除多余空白
        text_with_spaces = "  开头有空格  \n  每行都有空格  \n  结尾有空格  "
        cleaned = self.text_service.clean_text_basic(text_with_spaces)
        lines = cleaned.split("\n")
        for line in lines:
            assert not line.startswith(" ")
            assert not line.endswith(" ")

    def test_format_chapter_titles(self):
        """测试章节标题格式化"""
        text = """这是前言

第一章 开始的故事

这是第一章的内容。

第二章 继续的冒险

这是第二章的内容。

Chapter 3 English Title

This is chapter 3 content."""

        formatted_text, chapter_count = self.text_service.format_chapter_titles(text)

        # 应该检测到3个章节
        assert chapter_count == 3

        # 应该包含格式化的分隔符
        assert (
            "==================== 第一章 开始的故事 ===================="
            in formatted_text
        )
        assert (
            "==================== 第二章 继续的冒险 ===================="
            in formatted_text
        )
        assert (
            "==================== Chapter 3 English Title ===================="
            in formatted_text
        )

    def test_format_paragraphs(self):
        """测试段落格式化"""
        text = """这是一个很长的段落，需要进行自动换行处理。这个段落包含了很多内容，超过了设定的行宽限制。

这是第二个段落。"""

        formatted = self.text_service.format_paragraphs(
            text, max_line_width=30, enable_indent=True, indent_spaces=4
        )

        lines = formatted.split("\n")
        for line in lines:
            if line.strip():  # 非空行
                # 检查行宽
                assert len(line) <= 30
                # 检查缩进（如果不是章节标题）
                if "=" not in line:
                    assert line.startswith("    ") or line.strip() == ""

    def test_format_novel_complete(self):
        """测试完整的小说格式化流程"""
        sample_novel = """第一章 初始

在一个遥远的国度里，有一个名叫艾莉丝的女孩。她生活在一个偏僻的小村庄，每天都过着平静而简单的日子。她喜欢在阳光下阅读，梦想着有一天能够走出这个村庄，去看看外面的世界。

村子里的人都很友善，但艾莉丝总觉得有些东西在召唤着她。她常常凝视着远方的山脉，想象着山那边的世界。

第二章 探险的开始

一天，一个神秘的旅行者来到了村庄。他带来了许多新奇的故事和来自远方的物品。艾莉丝被他的故事深深吸引，决定跟随他踏上探险的旅程。"""

        result = self.text_service.format_novel(
            content=sample_novel,
            max_line_width=60,
            enable_paragraph_indent=True,
            indent_spaces=4,
            enable_chapter_formatting=True,
        )

        assert result["success"] is True
        assert result["chapter_count"] == 2
        assert result["formatted_content"] is not None
        assert result["original_length"] > 0
        assert result["formatted_length"] > 0
        assert result["processing_time"] > 0

    def test_clean_text_interface(self):
        """测试文本清理接口"""
        content = "\ufeff  测试内容  \r\n\r\n  另一行  \r\n"

        result = self.text_service.clean_text(
            content=content,
            remove_bom=True,
            normalize_line_breaks=True,
            remove_extra_whitespace=True,
            remove_empty_lines=True,
        )

        assert result["success"] is True
        assert result["cleaned_content"] is not None
        assert result["original_length"] == len(content)
        assert result["processing_time"] > 0

    def test_empty_content_handling(self):
        """测试空内容处理"""
        result = self.text_service.format_novel(content="")
        assert result["success"] is True
        assert result["formatted_content"] == ""

        result = self.text_service.clean_text(content="")
        assert result["success"] is True
        assert result["cleaned_content"] == ""

    def test_custom_chapter_pattern(self):
        """测试自定义章节标题模式"""
        text = """Chapter One: The Beginning

This is the content of chapter one.

Chapter Two: The Adventure

This is the content of chapter two."""

        custom_pattern = r"^Chapter\s+\w+:\s+.*$"

        formatted_text, chapter_count = self.text_service.format_chapter_titles(
            text, chapter_pattern=custom_pattern
        )

        assert chapter_count == 2
        assert (
            "==================== Chapter One: The Beginning ===================="
            in formatted_text
        )
        assert (
            "==================== Chapter Two: The Adventure ===================="
            in formatted_text
        )




if __name__ == "__main__":
    pytest.main([__file__])
