"""
LLM管理器 - 统一管理工作流和客户端
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from .client import LLMClient
from .config import LLMConfig, TaskType
from .langchain_manager import LangChainLLMManager
from .workflows.base import BaseWorkflow
from .workflows.novel_processing import NovelProcessingWorkflow

logger = logging.getLogger(__name__)


class LLMManager:
    """LLM管理器"""

    def __init__(self, config: Optional[LLMConfig] = None, use_langchain: bool = True):
        self.use_langchain = use_langchain

        if use_langchain:
            # 使用新的 LangChain 管理器
            self.langchain_manager = LangChainLLMManager()
            self.config = self.langchain_manager.config
            self.client = self.langchain_manager.client
        else:
            # 使用原有的 LiteLLM 客户端
            self.config = config or LLMConfig()
            self.client = LLMClient(self.config)
            self.langchain_manager = None

        self.workflows: Dict[str, BaseWorkflow] = {}
        self.active_workflows: Dict[str, Dict[str, Any]] = {}

        # 注册工作流
        self._register_workflows()

        # 验证配置
        if hasattr(self.config, 'validate_config') and not self.config.validate_config():
            logger.warning("LLM配置验证失败，某些功能可能不可用")

    def _register_workflows(self):
        """注册工作流"""
        logger.info("开始注册LLM工作流...")
        self.workflows = {
            "novel_processing": NovelProcessingWorkflow(
                llm_client=self.client,
                use_checkpointer=True,
                use_langchain=self.use_langchain
            )
        }
        logger.info(f"已注册 {len(self.workflows)} 个工作流")

    def list_workflows(self) -> List[str]:
        """列出可用的工作流"""
        logger.debug("列出可用工作流")
        return list(self.workflows.keys())

    def get_workflow(self, workflow_name: str) -> Optional[BaseWorkflow]:
        """获取工作流实例"""
        logger.debug(f"尝试获取工作流: {workflow_name}")
        workflow = self.workflows.get(workflow_name)
        if workflow:
            logger.debug(f"找到工作流: {workflow_name}")
        else:
            logger.warning(f"未找到工作流: {workflow_name}")
        return workflow

    def start_workflow(self, workflow_name: str, **kwargs) -> Dict[str, Any]:
        """启动工作流"""
        logger.info(f"请求启动工作流: {workflow_name}")
        workflow = self.get_workflow(workflow_name)
        if not workflow:
            logger.error(f"启动失败：未找到工作流: {workflow_name}")
            return {"success": False, "error": f"未找到工作流: {workflow_name}"}

        try:
            # 获取初始状态
            initial_state = workflow.get_initial_state(**kwargs)
            workflow_id = initial_state["workflow_id"]

            # 记录活跃工作流
            self.active_workflows[workflow_id] = {
                "workflow_name": workflow_name,
                "workflow": workflow,
                "state": initial_state,
                "started_at": datetime.now().isoformat(),
            }

            logger.info(f"成功启动工作流 {workflow_name}，ID: {workflow_id}")

            return {
                "success": True,
                "workflow_id": workflow_id,
                "initial_state": initial_state,
            }

        except Exception as e:
            logger.error(f"启动工作流 {workflow_name} 失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def run_workflow(
        self, workflow_id: str, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """运行工作流"""
        logger.info(f"请求运行工作流，ID: {workflow_id}")
        if workflow_id not in self.active_workflows:
            logger.error(f"运行失败：未找到活跃工作流: {workflow_id}")
            return {"success": False, "error": f"未找到活跃工作流: {workflow_id}"}

        try:
            workflow_info = self.active_workflows[workflow_id]
            workflow = workflow_info["workflow"]
            initial_state = workflow_info["state"]

            # 运行工作流
            logger.debug(f"开始执行工作流 {workflow_id}")
            result = workflow.run(initial_state, config)

            # 更新状态
            workflow_info["state"] = result
            workflow_info["completed_at"] = datetime.now().isoformat()

            logger.info(f"工作流 {workflow_id} 执行完成，状态: {result.get('status')}")

            return {"success": True, "workflow_id": workflow_id, "result": result}

        except Exception as e:
            logger.error(f"运行工作流 {workflow_id} 失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def stream_workflow(
        self, workflow_id: str, config: Optional[Dict[str, Any]] = None
    ):
        """流式运行工作流"""
        logger.info(f"请求流式运行工作流，ID: {workflow_id}")
        if workflow_id not in self.active_workflows:
            logger.error(f"流式运行失败：未找到活跃工作流: {workflow_id}")
            yield {"success": False, "error": f"未找到活跃工作流: {workflow_id}"}
            return

        try:
            workflow_info = self.active_workflows[workflow_id]
            workflow = workflow_info["workflow"]
            initial_state = workflow_info["state"]

            # 流式运行工作流
            logger.debug(f"开始流式执行工作流 {workflow_id}")
            for step_result in workflow.stream_run(initial_state, config):
                # 更新状态
                if isinstance(step_result, dict) and "workflow_id" in step_result:
                    workflow_info["state"] = step_result
                    logger.debug(
                        f"工作流 {workflow_id} 状态更新: {step_result.get('step', '未知步骤')}"
                    )

                yield {
                    "success": True,
                    "workflow_id": workflow_id,
                    "step_result": step_result,
                }

            # 标记完成
            workflow_info["completed_at"] = datetime.now().isoformat()
            logger.info(f"工作流 {workflow_id} 流式执行完成")

        except Exception as e:
            logger.error(f"流式运行工作流 {workflow_id} 失败: {str(e)}")
            yield {"success": False, "error": str(e)}

    def get_workflow_state(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        logger.debug(f"请求获取工作流 {workflow_id} 状态")
        if workflow_id not in self.active_workflows:
            logger.warning(f"工作流 {workflow_id} 未处于活跃状态，无法获取状态")
            return None

        workflow_info = self.active_workflows[workflow_id]
        logger.info(f"成功获取工作流 {workflow_id} 状态")
        return workflow_info["state"]

    def pause_workflow(self, workflow_id: str) -> bool:
        """暂停工作流"""
        logger.info(f"请求暂停工作流: {workflow_id}")
        if workflow_id not in self.active_workflows:
            logger.warning(f"工作流 {workflow_id} 未处于活跃状态，无法暂停")
            return False

        workflow_info = self.active_workflows[workflow_id]
        workflow = workflow_info["workflow"]

        success = workflow.pause_workflow(workflow_id)
        if success:
            logger.info(f"工作流 {workflow_id} 已暂停")
        else:
            logger.error(f"暂停工作流 {workflow_id} 失败")
        return success

    def resume_workflow(self, workflow_id: str) -> bool:
        """恢复工作流"""
        logger.info(f"请求恢复工作流: {workflow_id}")
        if workflow_id not in self.active_workflows:
            logger.warning(f"工作流 {workflow_id} 未处于活跃状态，无法恢复")
            return False

        workflow_info = self.active_workflows[workflow_id]
        workflow = workflow_info["workflow"]

        success = workflow.resume_workflow(workflow_id)
        if success:
            logger.info(f"工作流 {workflow_id} 已恢复")
        else:
            logger.error(f"恢复工作流 {workflow_id} 失败")
        return success

    def cancel_workflow(self, workflow_id: str) -> bool:
        """取消工作流"""
        logger.info(f"请求取消工作流: {workflow_id}")
        if workflow_id not in self.active_workflows:
            logger.warning(f"工作流 {workflow_id} 未处于活跃状态，无法取消")
            return False

        workflow_info = self.active_workflows[workflow_id]
        workflow = workflow_info["workflow"]

        success = workflow.cancel_workflow(workflow_id)
        if success:
            # 从活跃工作流中移除
            del self.active_workflows[workflow_id]
            logger.info(f"工作流 {workflow_id} 已取消并从活跃列表中移除")
        else:
            logger.error(f"取消工作流 {workflow_id} 失败")

        return success

    def list_active_workflows(self) -> List[Dict[str, Any]]:
        """列出活跃的工作流"""
        logger.info("请求列出所有活跃工作流")
        result = []
        for workflow_id, info in self.active_workflows.items():
            result.append(
                {
                    "workflow_id": workflow_id,
                    "workflow_name": info["workflow_name"],
                    "status": info["state"].get("status"),
                    "progress": info["state"].get("processing_progress", 0),
                    "started_at": info["started_at"],
                    "completed_at": info.get("completed_at"),
                }
            )
        logger.info(f"找到 {len(result)} 个活跃工作流")
        return result

    def cleanup_completed_workflows(self, max_age_hours: int = 24):
        """清理已完成的工作流"""
        logger.info(f"开始清理已完成或失败的工作流 (保留 {max_age_hours} 小时内)")
        current_time = datetime.now()
        to_remove = []

        for workflow_id, info in self.active_workflows.items():
            state = info["state"]
            if state.get("status") in ["completed", "failed"]:
                # 检查是否超过最大保留时间
                completed_at = info.get("completed_at")
                if completed_at:
                    completed_time = datetime.fromisoformat(completed_at)
                    age_hours = (current_time - completed_time).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        to_remove.append(workflow_id)

        for workflow_id in to_remove:
            del self.active_workflows[workflow_id]
            logger.info(f"清理已完成的工作流: {workflow_id}")

        logger.info(f"完成清理，移除了 {len(to_remove)} 个工作流")
        return len(to_remove)

    def call_llm_directly(
        self,
        task_type: TaskType,
        messages: List[Dict[str, str]],
        model_name: Optional[str] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """直接调用LLM（不通过工作流）"""
        logger.info(
            f"直接调用LLM: 任务类型={task_type.value}, 模型={model_name if model_name else 'fallback'}"
        )

        if self.use_langchain and self.langchain_manager:
            # 使用 LangChain 管理器
            return self.langchain_manager.call_llm_directly(task_type, messages, **kwargs)
        else:
            # 使用原有的 LiteLLM 客户端
            if model_name:
                result = self.client.call_model(model_name, messages, **kwargs)
                logger.info(
                    f"直接模型调用结果: success={result.get('success')}, model={result.get('model')}"
                )
                return result
            else:
                result = self.client.call_with_fallback(task_type, messages, **kwargs)
                logger.info(
                    f"直接带故障转移调用结果: success={result.get('success')}, tried_models={result.get('tried_models')}"
                )
                return result

    def get_cost_summary(self) -> Dict[str, Any]:
        """获取成本摘要"""
        logger.info("获取LLM成本摘要")
        if self.use_langchain and self.langchain_manager:
            return self.langchain_manager.get_cost_summary()
        else:
            return self.client.get_cost_summary()

    def test_connections(self) -> Dict[str, bool]:
        """测试所有模型连接"""
        logger.info("开始测试所有LLM模型连接")
        if self.use_langchain and self.langchain_manager:
            return self.langchain_manager.test_connections()
        else:
            results = {}
            available_models = self.client.list_available_models()

            for model_name in available_models:
                logger.debug(f"测试模型 {model_name} 连接...")
                results[model_name] = self.client.test_connection(model_name)
                logger.debug(f"模型 {model_name} 连接测试结果: {results[model_name]}")

            logger.info("所有模型连接测试完成")
            return results

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        logger.info("获取LLM系统状态")
        return {
            "config_valid": self.config.validate_config(),
            "available_models": self.client.list_available_models(),
            "cost_summary": self.get_cost_summary(),
            "active_workflows_count": len(self.active_workflows),
            "model_connection_tests": self.test_connections(),
        }
