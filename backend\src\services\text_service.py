import re
import time
from typing import Optional, Tuple

from ..utils.logging_config import get_logger

logger = get_logger(__name__)


class TextService:
    """文本处理服务"""

    def format_novel(
        self,
        content: str,
        enable_paragraph_indent: bool = False,  # 默认关闭段落缩进
        indent_spaces: int = 4,
        enable_chapter_formatting: bool = True,
        chapter_pattern: Optional[str] = None,
        remove_extra_spaces: bool = True,
        normalize_line_breaks: bool = True,
        normalize_quotes: bool = True,
    ) -> dict:
        """
        对小说文本内容进行完整格式化

        Args:
            content: 原始小说内容
            enable_paragraph_indent: 是否启用段落缩进
            indent_spaces: 缩进空格数
            enable_chapter_formatting: 是否启用章节标题格式化
            chapter_pattern: 自定义章节标题正则表达式模式
            remove_extra_spaces: 是否移除多余空格
            normalize_line_breaks: 是否统一换行符
            normalize_quotes: 是否统一引号格式

        Returns:
            格式化结果字典
        """
        start_time = time.time()

        try:
            original_length = len(content)
            logger.info(f"开始格式化小说内容，原始长度: {original_length} 字符")

            # 1. 基本清理
            logger.debug("执行基本文本清理...")
            cleaned_text = self.clean_text_basic(
                content,
                remove_bom=True,
                normalize_line_breaks=normalize_line_breaks,
                remove_extra_whitespace=remove_extra_spaces,
                remove_empty_lines=True,
            )
            logger.debug(f"基本清理完成，长度: {len(cleaned_text)} 字符")

            # 2. 统一引号格式
            if normalize_quotes:
                logger.debug("统一引号格式...")
                cleaned_text = self.normalize_quotes(cleaned_text)
                logger.debug("引号格式统一完成")

            # 3. 章节标题格式化（简化版本）
            formatted_text = cleaned_text
            chapter_count = 0

            if enable_chapter_formatting:
                logger.debug("开始章节格式化...")
                formatted_text, chapter_count = self.format_chapter_titles(
                    cleaned_text, chapter_pattern, preserve_numbers=True
                )
                logger.debug(f"章节格式化完成，检测到 {chapter_count} 个章节")

            # 4. 段落格式化
            logger.debug("执行段落格式化...")
            formatted_text = self.format_paragraphs(
                formatted_text,
                max_line_width=80,
                enable_indent=enable_paragraph_indent,
                indent_spaces=indent_spaces,
            )
            logger.debug("段落格式化完成")

            end_time = time.time()
            processing_time = end_time - start_time
            logger.info(f"小说内容格式化完成，耗时: {processing_time:.2f} 秒")

            return {
                "success": True,
                "message": "小说内容格式化成功",
                "formatted_content": formatted_text,
                "original_length": original_length,
                "formatted_length": len(formatted_text),
                "processing_time": round(processing_time, 3),
                "chapter_count": chapter_count,
            }

        except Exception as e:
            logger.error(f"小说内容格式化失败: {e}")
            raise ValueError(f"小说内容格式化失败: {str(e)}")

    def clean_text_basic(
        self,
        text: str,
        remove_bom: bool = True,
        normalize_line_breaks: bool = True,
        remove_extra_whitespace: bool = True,
        remove_empty_lines: bool = True,
    ) -> str:
        """
        进行基本的文本清理

        Args:
            text: 原始文本
            remove_bom: 是否移除BOM头
            normalize_line_breaks: 是否统一换行符
            remove_extra_whitespace: 是否移除多余空白字符
            remove_empty_lines: 是否移除多余空行

        Returns:
            清理后的文本
        """
        try:
            # 去除BOM头
            if remove_bom and text.startswith("\ufeff"):
                text = text[1:]

            # 统一换行符
            if normalize_line_breaks:
                text = text.replace("\r\n", "\n").replace("\r", "\n")

            # 去除每行首尾的空白
            if remove_extra_whitespace:
                lines = text.split("\n")
                cleaned_lines = [line.strip() for line in lines]
                text = "\n".join(cleaned_lines)

            # 智能处理空行：删除普通段落间空行，但保留章节前后空行
            if remove_empty_lines:
                lines = text.split("\n")
                cleaned_lines = []

                for i, line in enumerate(lines):
                    if line.strip():  # 非空行
                        # 检查是否是章节标记（纯数字）
                        is_chapter = line.strip().isdigit()

                        if is_chapter:
                            # 章节标记：前面添加空行（如果前面有内容）
                            if cleaned_lines and cleaned_lines[-1].strip():
                                cleaned_lines.append("")
                            cleaned_lines.append(line.strip())
                            # 后面也添加空行（如果后面还有内容）
                            if i < len(lines) - 1:
                                cleaned_lines.append("")
                        else:
                            # 普通内容行
                            cleaned_lines.append(line.strip())
                    # 跳过空行（除了章节前后的空行会在上面添加）

                text = "\n".join(cleaned_lines)

            return text

        except Exception as e:
            logger.error(f"文本基本清理失败: {e}")
            raise ValueError(f"文本清理失败: {str(e)}")

    def normalize_quotes(self, text: str) -> str:
        """统一引号格式"""
        # 将各种引号统一为「」格式
        text = text.replace('"', "「").replace('"', "」")
        text = text.replace('"', "「").replace('"', "」")
        text = text.replace("'", "「").replace("'", "」")
        return text

    def format_chapter_titles(
        self,
        text: str,
        chapter_pattern: Optional[str] = None,
        preserve_numbers: bool = True,
    ) -> Tuple[str, int]:
        """
        识别并格式化章节标题

        Args:
            text: 原始文本
            chapter_pattern: 章节标题正则表达式模式
            preserve_numbers: 是否保留纯数字章节标记

        Returns:
            (格式化后的文本, 章节数量)
        """
        try:
            # 使用默认或自定义的章节模式
            if chapter_pattern is None:
                # 扩展的章节检测模式
                patterns = [
                    r"^第[一二三四五六七八九十百千万零\d]+章[^的]*$",  # 第X章（但不包含"的"字，避免匹配内容）
                    r"^Chapter\s+\d+.*$",  # Chapter N
                    r"^第[一二三四五六七八九十百千万零\d]+节[^的]*$",  # 第X节（但不包含"的"字）
                    r"^\d+$",  # 纯数字
                    r"^[第]?[一二三四五六七八九十百千万零\d]+[章节回]$",  # 更严格的章节格式，必须以章节回结尾
                ]
            else:
                patterns = [chapter_pattern]

            lines = text.split("\n")
            formatted_lines = []
            chapter_count = 0

            # 检查是否有序言（第一个数字章节标记之前的内容）
            has_prologue = False
            first_chapter_line = -1

            for i, line in enumerate(lines):
                line_stripped = line.strip()
                if line_stripped.isdigit() and len(line_stripped) <= 3:
                    first_chapter_line = i
                    break

            # 如果找到数字章节标记，检查之前是否有非空内容
            if first_chapter_line > 0:
                prologue_content = "\n".join(lines[:first_chapter_line]).strip()
                if prologue_content:
                    has_prologue = True

            for line in lines:
                line_stripped = line.strip()
                is_chapter = False

                # 只有非空行才检查是否为章节
                if line_stripped:
                    # 检查是否匹配章节模式
                    for pattern in patterns:
                        if re.match(pattern, line_stripped, re.IGNORECASE):
                            is_chapter = True
                            break

                    # 特殊处理：检查是否是纯数字章节标记
                    if line_stripped.isdigit() and len(line_stripped) <= 3:
                        is_chapter = True

                if is_chapter:
                    chapter_count += 1
                    # 统一格式化为单独一行的数字，从1开始
                    formatted_lines.append(str(chapter_count))
                else:
                    # 非章节行保持原样
                    formatted_lines.append(line_stripped if line_stripped else "")

            # 如果有序言，章节总数需要加1
            total_chapters = chapter_count + (1 if has_prologue else 0)

            logger.debug(f"章节格式化完成，检测到 {chapter_count} 个章节标题，{total_chapters} 个实际章节（包含序言）")
            return "\n".join(formatted_lines), total_chapters

        except Exception as e:
            logger.error(f"章节标题格式化失败: {e}")
            raise ValueError(f"章节标题格式化失败: {str(e)}")

    def format_paragraphs(
        self,
        text: str,
        max_line_width: int = 80,
        enable_indent: bool = False,  # 默认关闭缩进
        indent_spaces: int = 4,
    ) -> str:
        """
        格式化段落，现代小说格式不需要段落间空行

        Args:
            text: 原始文本
            max_line_width: 每行最大字符数
            enable_indent: 是否启用段落缩进
            indent_spaces: 缩进空格数

        Returns:
            格式化后的文本
        """
        try:
            lines = text.split("\n")
            formatted_lines = []
            indent_str = " " * indent_spaces if enable_indent else ""

            for line in lines:
                if line.strip():
                    # 检查是否是章节标题（包含分隔符的行或纯数字章节）
                    if ("=" in line and len(line) > 40) or line.strip().isdigit():
                        # 章节标题不做处理
                        formatted_lines.append(line.strip())
                    else:
                        # 对于现代小说，每行都是独立的段落
                        if enable_indent:
                            formatted_lines.append(indent_str + line.strip())
                        else:
                            formatted_lines.append(line.strip())
                else:
                    # 保留空行（章节前后的空行）
                    formatted_lines.append("")

            return "\n".join(formatted_lines)

        except Exception as e:
            logger.error(f"段落格式化失败: {e}")
            raise ValueError(f"段落格式化失败: {str(e)}")

    def clean_text(
        self,
        content: str,
        remove_bom: bool = True,
        normalize_line_breaks: bool = True,
        remove_extra_whitespace: bool = True,
        remove_empty_lines: bool = True,
    ) -> dict:
        """
        文本清理接口

        Args:
            content: 原始文本内容
            remove_bom: 是否移除BOM头
            normalize_line_breaks: 是否统一换行符
            remove_extra_whitespace: 是否移除多余空白字符
            remove_empty_lines: 是否移除多余空行

        Returns:
            清理结果字典
        """
        start_time = time.time()

        try:
            original_length = len(content)
            logger.info(f"开始文本清理，原始长度: {original_length} 字符")

            cleaned_content = self.clean_text_basic(
                content,
                remove_bom=remove_bom,
                normalize_line_breaks=normalize_line_breaks,
                remove_extra_whitespace=remove_extra_whitespace,
                remove_empty_lines=remove_empty_lines,
            )

            cleaned_length = len(cleaned_content)
            processing_time = time.time() - start_time
            logger.info(
                f"文本清理完成，清理后长度: {cleaned_length} 字符，耗时: {processing_time:.2f} 秒"
            )

            return {
                "success": True,
                "message": "文本清理成功",
                "cleaned_content": cleaned_content,
                "original_length": original_length,
                "cleaned_length": cleaned_length,
                "processing_time": round(processing_time, 3),
            }

        except Exception as e:
            processing_time = max(time.time() - start_time, 0.001)
            logger.error(f"文本清理失败: {e}")
            raise ValueError(f"文本清理失败: {str(e)}")
