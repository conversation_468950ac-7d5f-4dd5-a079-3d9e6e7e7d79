"""章节处理模块

包含章节分割、验证等相关功能
"""

import os
from datetime import datetime
from typing import List

from ...utils.logging_config import get_logger

logger = get_logger(__name__)

# 避免循环导入问题，确保日志系统优先初始化
from .langsmith_config import log_workflow_metrics, with_langsmith_tracing  # noqa: E402


class ChapterProcessor:
    """章节处理器"""

    def __init__(self, workflow_instance):
        """
        初始化章节处理器

        Args:
            workflow_instance: 主工作流实例，用于访问共享方法
        """
        self.workflow = workflow_instance

    def split_chapters_by_numbers(self, content: str) -> List[str]:
        """
        根据单独行的数字分割章节

        Args:
            content: 待分割的小说内容

        Returns:
            分割后的章节内容列表
        """
        # 查找单独行的数字（章节分割符）
        lines = content.split("\n")
        chapter_starts = []

        for i, line in enumerate(lines):
            stripped_line = line.strip()
            # 检查是否为单独行的数字
            if stripped_line.isdigit():
                chapter_starts.append(i)

        if not chapter_starts:
            # 如果没有找到数字分割符，返回整个内容作为一章
            return [content.strip()]

        chapters = []

        # 处理第一个数字分割符之前的内容（序言或第0章）
        if chapter_starts[0] > 0:
            prologue_lines = lines[: chapter_starts[0]]
            prologue_content = "\n".join(prologue_lines).strip()
            if prologue_content:  # 只添加非空内容
                chapters.append(prologue_content)

        # 处理数字分割符之后的章节
        for i, start_idx in enumerate(chapter_starts):
            # 确定章节结束位置
            if i + 1 < len(chapter_starts):
                end_idx = chapter_starts[i + 1]
            else:
                end_idx = len(lines)

            # 提取章节内容（跳过分割符数字行）
            chapter_lines = lines[start_idx + 1 : end_idx]
            chapter_content = "\n".join(chapter_lines).strip()

            if chapter_content:  # 只添加非空章节
                chapters.append(chapter_content)

        return chapters if chapters else [content.strip()]

    @with_langsmith_tracing(
        run_name="split_chapters",
        tags=["chapter_splitting"],
    )
    def split_chapters(self, state):
        """章节分割节点"""
        logger.info(f"{state['workflow_id']}: 开始章节分割")

        # 记录开始指标
        log_workflow_metrics(
            state,
            "split_chapters",
            {
                "novel_length": len(state["novel_content"]),
                "novel_title": state["novel_title"],
            },
        )

        try:
            novel_content = state["novel_content"]

            # 进行章节分割
            chapter_contents = self.split_chapters_by_numbers(novel_content)
            logger.info(
                f"{state['workflow_id']}: 检测到 {len(chapter_contents)} 个章节"
            )

            return {
                **state,
                "step": "validate_input",
                "chapter_contents": chapter_contents,
                "total_chapters": len(chapter_contents),
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 0.1,
            }

        except Exception as e:
            return self.workflow.handle_error(state, e)

    def get_max_chapter_length(self) -> int:
        """获取章节最大长度限制"""
        return int(os.getenv("MAX_CHAPTER_LENGTH", "5000"))

    @with_langsmith_tracing(
        run_name="validate_input",
        tags=["validation"],
        exclude_fields=[],  # 不排除字段，确保状态正确传递
    )
    def validate_input(self, state):
        """验证输入数据节点"""
        logger.info(f"{state['workflow_id']}: 开始验证输入数据")

        # 记录开始指标
        log_workflow_metrics(
            state,
            "validate_input",
            {
                "novel_length": len(state["novel_content"]),
                "novel_title": state["novel_title"],
            },
        )

        try:
            novel_content = state["novel_content"]
            chapter_contents = state["chapter_contents"]

            # 基本验证
            if not novel_content or len(novel_content.strip()) < 100:
                raise ValueError("小说内容太短，至少需要100个字符")

            # 检查章节分割结果
            if not chapter_contents or len(chapter_contents) == 0:
                raise ValueError("章节分割失败，未检测到任何章节")

            # 检查内容格式
            if not any(
                char in novel_content
                for char in ["「", "『", '"', '"', "'", '"', "[", "【"]
            ):
                logger.warning(f"{state['workflow_id']}: 未检测到对话标记")

            # 章节长度校验
            max_chapter_length = self.get_max_chapter_length()
            invalid_chapters = []

            for i, chapter_content in enumerate(chapter_contents, 1):
                chapter_length = len(chapter_content)
                if chapter_length > max_chapter_length:
                    invalid_chapters.append(
                        {
                            "chapter": i,
                            "length": chapter_length,
                            "max_allowed": max_chapter_length,
                        }
                    )

            if invalid_chapters:
                error_msg = f"以下章节超过最大长度限制 ({max_chapter_length} 字符):\n"
                for invalid in invalid_chapters:
                    error_msg += f"第{invalid['chapter']}章: {invalid['length']} 字符\n"
                raise ValueError(error_msg)

            logger.info(
                f"{state['workflow_id']}: 输入验证通过，内容长度: {len(novel_content)} 字符，共 {len(chapter_contents)} 个章节"
            )

            # 调试：确认返回状态包含chapter_contents
            result_state = {
                **state,
                "step": "analyze_characters",
                "chapter_contents": chapter_contents,  # 明确包含章节内容
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 0.2,
            }
            logger.debug(f"{state['workflow_id']}: 返回状态包含chapter_contents: {'chapter_contents' in result_state}")
            logger.debug(f"{state['workflow_id']}: 返回状态chapter_contents长度: {len(result_state.get('chapter_contents', []))}")

            return result_state

        except Exception as e:
            return self.workflow.handle_error(state, e)
