"""
LangChain LLM 管理器

使用 LangChain 官方的 LLM 调用方案替换 LiteLLM
管理工作流和直接 LLM 调用
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime

from .langchain_client import LangChainLLMClient
from .langchain_config import LangChainLLMConfig, TaskType
from ..utils.logging_config import get_logger

logger = get_logger(__name__)


class LangChainLLMManager:
    """LangChain LLM 管理器"""

    def __init__(self, config: Optional[LangChainLLMConfig] = None):
        self.config = config or LangChainLLMConfig()
        self.client = LangChainLLMClient(self.config)
        self.active_workflows: Dict[str, Any] = {}
        
        logger.info("LangChain LLM 管理器初始化完成")

    def call_llm_directly(
        self, 
        task_type: TaskType, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> Dict[str, Any]:
        """直接调用 LLM（用于工作流）"""
        logger.info(f"直接调用 LLM，任务类型: {task_type.value}")
        
        try:
            result = self.client.call_with_fallback(task_type, messages, **kwargs)
            
            if result["success"]:
                logger.info(f"LLM 调用成功，模型: {result['model']}")
                if "cost" in result:
                    logger.info(f"调用成本: ¥{result['cost']:.4f}")
            else:
                logger.error(f"LLM 调用失败: {result.get('error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"LLM 直接调用异常: {e}")
            return {"success": False, "error": str(e)}

    def register_workflow(self, workflow_id: str, workflow_instance: Any):
        """注册工作流实例"""
        self.active_workflows[workflow_id] = {
            "instance": workflow_instance,
            "created_at": datetime.now(),
            "status": "active"
        }
        logger.info(f"注册工作流: {workflow_id}")

    def unregister_workflow(self, workflow_id: str):
        """注销工作流实例"""
        if workflow_id in self.active_workflows:
            del self.active_workflows[workflow_id]
            logger.info(f"注销工作流: {workflow_id}")

    def get_active_workflows(self) -> Dict[str, Any]:
        """获取活跃工作流列表"""
        return {
            wf_id: {
                "created_at": wf_info["created_at"].isoformat(),
                "status": wf_info["status"]
            }
            for wf_id, wf_info in self.active_workflows.items()
        }

    def get_cost_summary(self) -> Dict[str, Any]:
        """获取成本摘要"""
        logger.info("获取LLM成本摘要")
        base_summary = self.client.get_cost_summary()
        
        # 添加预算信息
        daily_cost = base_summary.get("daily_cost", 0)
        daily_budget = self.config.cost_limits["daily_budget"]
        
        return {
            **base_summary,
            "daily_budget": daily_budget,
            "remaining_budget": max(0, daily_budget - daily_cost),
            "budget_usage_percent": (daily_cost / daily_budget * 100) if daily_budget > 0 else 0,
            "active_workflows": len(self.active_workflows),
        }

    def test_connections(self) -> Dict[str, bool]:
        """测试所有模型连接"""
        logger.info("开始测试所有LLM模型连接")
        results = {}
        available_models = self.client.list_available_models()

        for model_name in available_models:
            logger.debug(f"测试模型 {model_name} 连接...")
            results[model_name] = self.client.test_connection(model_name)
            logger.debug(f"模型 {model_name} 连接测试结果: {results[model_name]}")

        logger.info("所有模型连接测试完成")
        return results

    def validate_configuration(self) -> Dict[str, Any]:
        """验证配置"""
        logger.info("验证 LangChain LLM 配置")
        
        validation_result = {
            "config_valid": False,
            "missing_env_vars": [],
            "available_models": [],
            "connection_tests": {},
            "errors": []
        }
        
        try:
            # 验证基础配置
            validation_result["config_valid"] = self.config.validate_config()
            
            # 获取可用模型
            validation_result["available_models"] = self.client.list_available_models()
            
            # 测试连接（仅测试主要模型以节省时间）
            primary_models = set()
            for task_config in self.config.tasks.values():
                primary_models.add(task_config.primary_model)
            
            for model_name in primary_models:
                if model_name in validation_result["available_models"]:
                    validation_result["connection_tests"][model_name] = self.client.test_connection(model_name)
            
            logger.info("配置验证完成")
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            validation_result["errors"].append(str(e))
        
        return validation_result

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        models_info = {}
        
        for model_name, model_config in self.config.models.items():
            models_info[model_name] = {
                "display_name": model_config.display_name,
                "provider": model_config.provider.value,
                "max_output_tokens": model_config.max_output_tokens,
                "context_length": model_config.context_length,
                "input_cost_per_1k": model_config.input_cost_per_1k_tokens,
                "output_cost_per_1k": model_config.output_cost_per_1k_tokens,
                "temperature": model_config.temperature,
            }
        
        return models_info

    def get_task_info(self) -> Dict[str, Any]:
        """获取任务配置信息"""
        tasks_info = {}
        
        for task_type, task_config in self.config.tasks.items():
            tasks_info[task_type.value] = {
                "primary_model": task_config.primary_model,
                "fallback_models": task_config.fallback_models,
                "budget_limit": task_config.budget_limit,
                "max_retries": task_config.max_retries,
                "quality_threshold": task_config.quality_threshold,
            }
        
        return tasks_info

    async def shutdown(self):
        """关闭管理器"""
        logger.info("关闭 LangChain LLM 管理器")
        
        # 清理活跃工作流
        for workflow_id in list(self.active_workflows.keys()):
            self.unregister_workflow(workflow_id)
        
        # 清理模型缓存
        if hasattr(self.client, '_model_cache'):
            self.client._model_cache.clear()
        
        logger.info("LangChain LLM 管理器已关闭")


# 全局管理器实例
llm_manager = LangChainLLMManager()
