import { useTTSStore } from '../store/ttsStore';
import { useMemo, useState, useEffect } from 'react';
import { diffWords, type Change } from 'diff';

// 为AI返回的对话条目定义一个类型，增强代码可读性和健壮性
interface DialogueItem {
  speaker: string;
  emotion: string;
  content: string;
  type: string;
}

// 情绪选项
const EMOTION_OPTIONS = [
  { value: 'neutral', label: '平静' },
  { value: 'happy', label: '愉悦' },
  { value: 'angry', label: '愤怒' },
  { value: 'sad', label: '悲伤' },
  { value: 'surprised', label: '惊讶' },
  { value: 'fearful', label: '恐惧' },
  { value: 'uncertain', label: '无法确定' },
];

export default function ProcessingResultPanel() {
  const {
    isProcessingNovel,
    processingProgress,
    processingStatus,
    processingResult,
    processingError,
    resetNovelProcessing,
    novel,
    isComparing,
    compareMode,
    setIsComparing,
    setCompareMode,
    showDialogueSplit,
    setShowDialogueSplit,
    updateDialogueItem,
    characters, // 添加角色列表
  } = useTTSStore();

  const handleReset = () => {
    resetNovelProcessing();
  };

  // 可编辑对话项组件
  const EditableDialogueItem = ({
    item,
    chapterTitle,
    itemIndex
  }: {
    item: DialogueItem;
    chapterTitle: string;
    itemIndex: number;
  }) => {
    // 本地状态，避免每次输入都触发全局状态更新
    const [localSpeaker, setLocalSpeaker] = useState(item.speaker);
    const [localContent, setLocalContent] = useState(item.content);
    const [localEmotion, setLocalEmotion] = useState(item.emotion || 'neutral');

    // 获取角色名选项，包括当前角色和已识别的角色
    const characterOptions = useMemo(() => {
      const options = new Set<string>();

      // 添加当前角色
      if (item.speaker && item.speaker.trim()) {
        options.add(item.speaker);
      }

      // 添加已识别的角色
      characters.forEach(char => {
        if (char.name && char.name.trim()) {
          options.add(char.name);
        }
      });

      // 添加常用角色
      options.add('旁白');

      return Array.from(options).sort();
    }, [item.speaker, characters]);

    // 用于跟踪是否正在输入中文
    const [isComposing, setIsComposing] = useState(false);

    // 防抖保存函数 - 考虑中文输入状态
    useEffect(() => {
      // 如果正在输入中文，延长防抖时间
      const delay = isComposing ? 3000 : 2000;

      const timeoutId = setTimeout(() => {
        if (!isComposing && (localSpeaker !== item.speaker || localContent !== item.content || localEmotion !== item.emotion)) {
          updateDialogueItem(chapterTitle, itemIndex, {
            speaker: localSpeaker,
            content: localContent,
            emotion: localEmotion,
          });
        }
      }, delay);

      return () => clearTimeout(timeoutId);
    }, [localSpeaker, localContent, localEmotion, isComposing, chapterTitle, itemIndex, item.speaker, item.content, item.emotion, updateDialogueItem]);

    // 当外部数据更新时，同步本地状态
    useEffect(() => {
      setLocalSpeaker(item.speaker);
      setLocalContent(item.content);
      setLocalEmotion(item.emotion || 'neutral');
    }, [item.speaker, item.content, item.emotion]);

    return (
      <div className="p-3 rounded-md bg-neutral-50 border">
        <div className="flex items-start gap-2 mb-2 flex-wrap">
          {/* 角色名下拉选择框 - 使用 flex-shrink-0 防止被压缩 */}
          <select
            value={localSpeaker}
            onChange={(e) => setLocalSpeaker(e.target.value)}
            className="font-semibold text-primary-600 bg-transparent border-0 outline-none focus:ring-1 focus:ring-primary-300 rounded px-1 py-0.5 flex-shrink-0 cursor-pointer"
            style={{
              minWidth: '80px',
              width: `${Math.max(localSpeaker.length * 12 + 40, 80)}px` // 为下拉箭头留出空间
            }}
          >
            {characterOptions.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>

          {/* 情绪下拉框 */}
          <select
            value={localEmotion}
            onChange={(e) => setLocalEmotion(e.target.value)}
            className="text-xs font-normal text-neutral-600 bg-white border border-neutral-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary-300 flex-shrink-0"
          >
            {/* 如果当前值是"uncertain"，显示它但标记为不可选 */}
            {localEmotion === 'uncertain' && (
              <option value="uncertain" disabled>
                无法确定
              </option>
            )}
            {/* 显示除"uncertain"外的所有选项 */}
            {EMOTION_OPTIONS.filter(option => option.value !== 'uncertain').map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* 对话内容编辑框 */}
        <textarea
          value={localContent}
          onChange={(e) => {
            setLocalContent(e.target.value);
            // 立即调整高度
            const target = e.target as HTMLTextAreaElement;
            target.style.height = 'auto';
            // 使用 scrollHeight 来获取实际需要的高度，但限制最小值
            const minHeight = 26;
            const actualHeight = Math.max(target.scrollHeight, minHeight);
            target.style.height = actualHeight + 'px';
          }}
          onCompositionStart={() => setIsComposing(true)}
          onCompositionEnd={() => setIsComposing(false)}
          className="w-full mt-1 text-neutral-800 bg-transparent border-0 outline-none focus:ring-1 focus:ring-primary-300 rounded px-1 py-0.5 resize-none overflow-hidden"
          style={{
            minHeight: '26px',
            height: 'auto',
            lineHeight: '1.375'
          }}
          placeholder="对话内容"
          ref={(textarea) => {
            if (textarea) {
              // 初始化时设置正确的高度
              textarea.style.height = 'auto';
              const minHeight = 26;
              const actualHeight = Math.max(textarea.scrollHeight, minHeight);
              textarea.style.height = actualHeight + 'px';
            }
          }}
        />
      </div>
    );
  };

  const reconstructedText = useMemo(() => {
    if (!processingResult?.chapters) return '';
    return Object.values(processingResult.chapters)
      .flat()
      .map((item: any) => item.content)
      .join('\n');
  }, [processingResult]);

  const differences = useMemo(() => {
    if (!isComparing) return [];

    let baseText = '';
    if (compareMode === 'original') {
      // 与原文对比：移除原始文本中的数字标记 (如 '1\n')，以便更准确地比较
      baseText = novel.content.replace(/^\d+\s*$/gm, '');
    } else {
      // 与最终结果对比：使用重构的文本作为基准
      baseText = reconstructedText;
    }

    const targetText =
      compareMode === 'original'
        ? reconstructedText
        : novel.content.replace(/^\d+\s*$/gm, '');

    return diffWords(baseText, targetText, {
      ignoreWhitespace: true,
    });
  }, [isComparing, compareMode, novel.content, reconstructedText]);

  return (
    <div className="flex flex-col h-full bg-neutral-50 border-l border-neutral-200">
      <div className="p-2 border-b border-neutral-200 flex justify-between items-center pl-[16px] pr-[16px] pt-[4px] pb-[4px]">
        <h2 className="text-sm font-semibold">AI 处理结果</h2>
        <div className="flex items-center space-x-4">
          {processingResult && (
            <>
              <label className="flex h-8 items-center cursor-pointer gap-2 px-2 rounded-lg hover:bg-neutral-100">
                <span className="text-sm">对比差异</span>
                <input
                  type="checkbox"
                  className="toggle toggle-sm toggle-primary"
                  checked={isComparing}
                  onChange={(e) => setIsComparing(e.target.checked)}
                />
              </label>
              <label
                className="flex h-8 items-center cursor-pointer gap-2 px-2 rounded-lg hover:bg-neutral-100"
              >
                <span className="text-sm">显示对话分割</span>
                <input
                  type="checkbox"
                  className="toggle toggle-sm toggle-primary"
                  checked={showDialogueSplit}
                  onChange={(e) => setShowDialogueSplit(e.target.checked)}
                />
              </label>
            </>
          )}
          <button onClick={handleReset} className="btn btn-ghost btn-sm">
            关闭
          </button>
        </div>
      </div>

      <div className="grow overflow-y-auto p-[8px]">
        {processingError ? (
          <div className="alert alert-error">
            <div className="flex-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="w-6 h-6 mx-2 stroke-current"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                ></path>
              </svg>
              <label>{processingError}</label>
            </div>
          </div>
        ) : isProcessingNovel || processingResult ? (
          <div className="space-y-4">
            {isProcessingNovel && (
              <>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    处理状态
                  </label>
                  <p className="text-sm font-medium text-primary-600">
                    {processingStatus}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    处理进度
                  </label>
                  <progress
                    className="progress progress-primary w-full"
                    value={processingProgress}
                    max="100"
                  ></progress>
                </div>
              </>
            )}

            {processingResult && (
              <div>
                <label className="text-sm font-medium text-gray-700">
                  {showDialogueSplit
                    ? '对话分割结果'
                    : isComparing
                      ? `差异对比结果 (${compareMode === 'original' ? '与原文对比' : '与最终结果对比'})`
                      : '对话分割结果'}
                </label>
                <div className="w-full bg-white rounded-md p-4 text-sm border space-y-4 max-h-[calc(100vh-220px)] overflow-y-auto">
                  {showDialogueSplit ? (
                    // 显示对话分割结果（原来的最终结果）
                    Object.entries(processingResult.chapters || {}).map(
                      ([chapterTitle, chapterContent], chapterIndex) => {
                        if (!Array.isArray(chapterContent)) return null;

                        return (
                          <div key={chapterIndex} className="space-y-3">
                            <h3 className="text-base font-semibold border-b pb-2 mb-2">
                              {chapterTitle}
                            </h3>
                            {(chapterContent as DialogueItem[]).map(
                              (item, itemIndex) => (
                                <EditableDialogueItem
                                  key={itemIndex}
                                  item={item}
                                  chapterTitle={chapterTitle}
                                  itemIndex={itemIndex}
                                />
                              )
                            )}
                          </div>
                        );
                      }
                    )
                  ) : isComparing ? (
                    <div className="whitespace-pre-wrap leading-relaxed">
                      {differences.map((part: Change, index: number) => {
                        const style = {
                          backgroundColor: part.added
                            ? 'rgba(67, 177, 88, 0.2)'
                            : part.removed
                              ? 'rgba(239, 68, 68, 0.2)'
                              : 'transparent',
                          textDecoration: part.removed
                            ? 'line-through'
                            : 'none',
                          padding: '2px 0',
                          borderRadius: '3px',
                        };
                        return (
                          <span key={index} style={style}>
                            {part.value}
                          </span>
                        );
                      })}
                    </div>
                  ) : (
                    Object.entries(processingResult.chapters || {}).map(
                      ([chapterTitle, chapterContent], chapterIndex) => {
                        // 确保 chapterContent 是一个数组，以防数据格式意外错误
                        if (!Array.isArray(chapterContent)) {
                          return null;
                        }

                        return (
                          <div key={chapterIndex} className="space-y-3">
                            <h3 className="text-base font-semibold border-b pb-2 mb-2">
                              {chapterTitle}
                            </h3>
                            {(chapterContent as DialogueItem[]).map(
                              (item, itemIndex) => (
                                <EditableDialogueItem
                                  key={itemIndex}
                                  item={item}
                                  chapterTitle={chapterTitle}
                                  itemIndex={itemIndex}
                                />
                              )
                            )}
                          </div>
                        );
                      }
                    )
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center text-neutral-500 pt-16">
            <span className="loading loading-spinner loading-lg"></span>
            <p className="mt-4">等待处理任务...</p>
          </div>
        )}
      </div>
    </div>
  );
}
