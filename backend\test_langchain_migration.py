#!/usr/bin/env python3
"""
LangChain 迁移测试脚本

测试新的 LangChain 实现是否正常工作
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.llm.langchain_config import LangChainLLMConfig, TaskType
from src.llm.langchain_client import LangChainLLMClient
from src.llm.langchain_manager import LangChainLLMManager
from src.utils.logging_config import get_logger

logger = get_logger(__name__)


def test_config():
    """测试配置模块"""
    print("=" * 50)
    print("测试 LangChain 配置模块")
    print("=" * 50)
    
    try:
        config = LangChainLLMConfig()
        
        print(f"[OK] 配置初始化成功")
        print(f"[OK] 可用模型数量: {len(config.models)}")
        print(f"[OK] 任务配置数量: {len(config.tasks)}")
        
        # 列出所有模型
        print("\n可用模型:")
        for model_name, model_config in config.models.items():
            print(f"  - {model_name}: {model_config.display_name} ({model_config.provider.value})")
        
        # 验证配置
        is_valid = config.validate_config()
        print(f"\n配置验证结果: {'[OK] 通过' if is_valid else '[FAIL] 失败'}")

        return True

    except Exception as e:
        print(f"[FAIL] 配置测试失败: {e}")
        return False


def test_client():
    """测试客户端模块"""
    print("\n" + "=" * 50)
    print("测试 LangChain 客户端模块")
    print("=" * 50)
    
    try:
        client = LangChainLLMClient()
        print(f"[OK] 客户端初始化成功")

        # 列出可用模型
        available_models = client.list_available_models()
        print(f"[OK] 可用模型: {available_models}")
        
        # 测试简单调用（如果有 API 密钥）
        test_messages = [{"role": "user", "content": "Hello, this is a test message. Please respond with 'Test successful'."}]
        
        print("\n测试模型连接:")
        for model_name in available_models[:2]:  # 只测试前两个模型
            try:
                print(f"  测试模型: {model_name}")
                result = client.call_model(model_name, test_messages)
                if result["success"]:
                    print(f"    [OK] 调用成功: {result['content'][:50]}...")
                    if "cost" in result:
                        print(f"    [OK] 成本: ${result['cost']:.4f}")
                else:
                    print(f"    [FAIL] 调用失败: {result.get('error')}")
            except Exception as e:
                print(f"    [FAIL] 连接测试异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 客户端测试失败: {e}")
        return False


def test_manager():
    """测试管理器模块"""
    print("\n" + "=" * 50)
    print("测试 LangChain 管理器模块")
    print("=" * 50)

    try:
        manager = LangChainLLMManager()
        print(f"[OK] 管理器初始化成功")

        # 测试配置验证
        validation_result = manager.validate_configuration()
        print(f"[OK] 配置验证: {'通过' if validation_result['config_valid'] else '失败'}")

        # 测试成本摘要
        cost_summary = manager.get_cost_summary()
        print(f"[OK] 成本摘要: 日成本 ${cost_summary.get('daily_cost', 0):.4f}")

        # 测试模型信息
        model_info = manager.get_model_info()
        print(f"[OK] 模型信息: {len(model_info)} 个模型")

        # 测试任务信息
        task_info = manager.get_task_info()
        print(f"[OK] 任务信息: {len(task_info)} 个任务类型")

        return True

    except Exception as e:
        print(f"[FAIL] 管理器测试失败: {e}")
        return False


def test_task_call():
    """测试任务调用"""
    print("\n" + "=" * 50)
    print("测试任务调用")
    print("=" * 50)
    
    try:
        manager = LangChainLLMManager()
        
        # 测试角色分析任务
        test_messages = [
            {"role": "user", "content": "请分析以下文本中的角色：小明是一个勇敢的少年。"}
        ]
        
        print("测试角色分析任务:")
        result = manager.call_llm_directly(TaskType.CHARACTER_ANALYSIS, test_messages)
        
        if result["success"]:
            print(f"  [OK] 任务调用成功")
            print(f"  [OK] 使用模型: {result.get('model')}")
            print(f"  [OK] 响应内容: {result['content'][:100]}...")
            if "cost" in result:
                print(f"  [OK] 调用成本: ${result['cost']:.4f}")
        else:
            print(f"  [FAIL] 任务调用失败: {result.get('error')}")

        return result["success"]

    except Exception as e:
        print(f"[FAIL] 任务调用测试失败: {e}")
        return False


def test_environment():
    """测试环境变量"""
    print("\n" + "=" * 50)
    print("检查环境变量")
    print("=" * 50)
    
    required_vars = [
        "GOOGLE_API_KEY",
        "OPENROUTER_API_KEY",
    ]
    
    missing_vars = []
    for var in required_vars:
        if os.getenv(var):
            print(f"[OK] {var}: 已设置")
        else:
            print(f"[FAIL] {var}: 未设置")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n[WARNING] 缺少环境变量: {missing_vars}")
        print("某些功能可能无法正常工作")
        return False
    else:
        print("\n[OK] 所有必需的环境变量都已设置")
        return True


def main():
    """主测试函数"""
    print("LangChain 迁移测试")
    print("=" * 50)
    
    # 测试结果
    results = {
        "environment": test_environment(),
        "config": test_config(),
        "client": test_client(),
        "manager": test_manager(),
    }
    
    # 如果基础测试通过，进行任务调用测试
    if all(results.values()):
        results["task_call"] = test_task_call()
    else:
        print("\n[WARNING] 跳过任务调用测试，因为基础测试未通过")
        results["task_call"] = False

    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)

    passed = sum(results.values())
    total = len(results)

    for test_name, result in results.items():
        status = "[OK] 通过" if result else "[FAIL] 失败"
        print(f"{test_name:15}: {status}")

    print(f"\n总体结果: {passed}/{total} 项测试通过")

    if passed == total:
        print("[SUCCESS] 所有测试通过！LangChain 迁移成功！")
        return 0
    else:
        print("[ERROR] 部分测试失败，请检查配置和环境变量")
        return 1


if __name__ == "__main__":
    sys.exit(main())
