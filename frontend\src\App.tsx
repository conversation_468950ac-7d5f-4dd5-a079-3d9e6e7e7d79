import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';
import { Toaster as HotToaster } from 'react-hot-toast';
import TTSPage from './pages/TTSPage';
import TTSEngineProvider from './components/TTSEngineProvider';

// TODO: 创建页面组件后导入
// import NovelEditorPage from './pages/NovelEditorPage';
// import AudioPlayerPage from './pages/AudioPlayerPage';

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient} data-oid="8gh1:ad">
      <TTSEngineProvider data-oid="c-pibg4">
        <Router data-oid="t-m_rbx">
          <div className="App" data-oid="vpmjqx3">
            <Routes data-oid="vzzeejg">
              <Route
                path="/"
                element={<TTSPage data-oid="pz:pilb" />}
                data-oid="q_._m8:"
              />

              {/* TODO: 添加更多路由 */}
              {/* <Route path="/editor" element={<NovelEditorPage />} /> */}
              {/* <Route path="/player" element={<AudioPlayerPage />} /> */}
            </Routes>

            {/* 通知组件 */}
            <Toaster position="top-right" data-oid="o57-jo-" />
            <HotToaster position="top-center" data-oid="gp4:bmj" />
          </div>
        </Router>
      </TTSEngineProvider>
    </QueryClientProvider>
  );
}

export default App;
