# ParrotNovels Dev Container 快速开始

## 🚀 一键启动

### 1. 启动基础环境（CPU）
```bash
# 在 VS Code 中打开项目
code .

# 选择 "Reopen in Container" 或按 Ctrl+Shift+P 搜索
# "Dev Containers: Reopen in Container"
```

### 2. 启动 GPU 环境（可选）
如果你有 NVIDIA GPU 并需要 CUDA 支持：

1. **编辑 `.devcontainer/devcontainer.json`**
   ```json
   {
     "dockerComposeFile": "docker-compose.gpu.yml",
     // ... 其他配置保持不变
   }
   ```

2. **重新启动容器**
   - 按 `Ctrl+Shift+P`
   - 选择 `Dev Containers: Rebuild Container`

## 🌐 服务地址

| 服务 | 地址 | 描述 |
|------|------|------|
| 后端 API | http://localhost:8000 | FastAPI 服务 |
| 前端应用 | http://localhost:5173 | React 开发服务器 |
| API 文档 | http://localhost:8000/docs | Swagger 文档 |
| LangGraph | http://localhost:2024 | 工作流开发界面 |

## 🔧 快速命令

### 启动所有服务
```bash
bash /workspace/start-all.sh
```

### 单独启动服务
```bash
# 后端
bash /workspace/start-backend.sh

# 前端
bash /workspace/start-frontend.sh

# LangGraph
bash /workspace/start-langgraph.sh
```

### 开发工具
```bash
# 激活 Python 环境
source /opt/conda/bin/activate parrot

# 运行测试
cd /workspace/backend && python -m pytest

# 格式化代码
black /workspace/backend/src/
prettier --write /workspace/frontend/src/
```

## 📝 环境变量

### 环境变量文件层次
1. **`.devcontainer/devcontainer.env`**: 容器专用配置（自动生效）
2. **`.env`**: 项目主配置文件（可选，覆盖容器配置）

### 创建主配置文件（可选）
```bash
cp .env.example .env
```

### 重要配置（在 .devcontainer/devcontainer.env 中）
编辑 `.devcontainer/devcontainer.env` 设置实际的 API 密钥：
- `OPENROUTER_API_KEY`: OpenRouter API 密钥
- `GEMINI_API_KEY`: Google AI Studio API 密钥
- `LANGSMITH_API_KEY`: LangSmith 监控密钥
- `MINIMAX_API_KEY`: Minimax TTS API 密钥

## 🐛 调试

### VS Code 调试配置
- 使用 F5 启动调试
- 选择 "Python: FastAPI Backend" 调试后端
- 选择 "Launch Full Stack" 同时调试前后端

### 查看日志
```bash
# 容器日志
docker logs parrot-novels-dev

# 应用日志
tail -f /workspace/resources/logs/app.log
```

## ❗ 常见问题

### 1. 端口占用
```bash
# 检查端口占用
lsof -i :8000
lsof -i :5173

# 或者修改 docker-compose.yml 中的端口映射
```

### 2. 依赖安装失败
```bash
# 重新运行初始化
bash .devcontainer/setup.sh

# 手动安装
pip install -r backend/requirements_llm.txt
cd frontend && bun install
```

### 3. GPU 不可用
```bash
# 检查 GPU 状态
nvidia-smi

# 验证 Docker GPU 支持
docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi
```

## 🔄 重新构建

如果需要重新构建容器：
```bash
# 在 VS Code 中
Ctrl+Shift+P -> "Dev Containers: Rebuild Container"

# 或者命令行
docker-compose -f .devcontainer/docker-compose.yml build --no-cache
```

## 📞 获取帮助

- 查看 `.devcontainer/README.md` 了解详细信息
- 检查 Docker 和 VS Code 日志
- 确保 Docker Desktop 正在运行 