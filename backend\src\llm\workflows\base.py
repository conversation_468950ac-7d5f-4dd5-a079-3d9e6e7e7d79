"""
工作流基类
"""

import json
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, TypedDict

try:
    from ...utils.logging_config import get_logger
except ImportError:
    # 备用导入方案
    import sys
    from pathlib import Path

    if str(Path(__file__).parent.parent.parent) not in sys.path:
        sys.path.insert(0, str(Path(__file__).parent.parent.parent))
    from backend.src.utils.logging_config import get_logger

logger = get_logger(__name__)

try:
    from langgraph.checkpoint.memory import MemorySaver
    from langgraph.graph import END, START, StateGraph
    from langgraph.prebuilt import ToolNode
except ImportError:
    logger.warning("langgraph未安装，部分功能可能受限。请运行: pip install langgraph")
    StateGraph = None
    START = None
    END = None
    ToolNode = None
    MemorySaver = None
    SqliteSaver = None

from ..client import LLMClient
from ..config import LLMConfig
from ..langchain_client import LangChainLLMClient
from ..langchain_config import LangChainLLMConfig


class BaseWorkflowState(TypedDict):
    """基础工作流状态"""

    workflow_id: str
    step: str
    status: str  # "running", "completed", "failed", "paused"
    error: Optional[str]
    created_at: str
    updated_at: str
    metadata: Dict[str, Any]


class BaseWorkflow(ABC):
    """工作流基类"""

    def __init__(
        self,
        llm_client: Optional[LLMClient] = None,
        config: Optional[LLMConfig] = None,
        use_checkpointer: bool = True,
        use_langchain: bool = True,  # 新参数：是否使用 LangChain
    ):
        # 根据参数选择使用 LangChain 或 LiteLLM
        if use_langchain:
            self.llm_client = LangChainLLMClient(LangChainLLMConfig())
            self.config = LangChainLLMConfig()
        else:
            self.llm_client = llm_client or LLMClient(config)
            self.config = config or LLMConfig()

        self.graph = None
        self.checkpointer = MemorySaver() if use_checkpointer and MemorySaver else None
        self._setup_workflow()

    @abstractmethod
    def _setup_workflow(self):
        """设置工作流图"""
        pass

    @abstractmethod
    def get_initial_state(self, **kwargs) -> Dict[str, Any]:
        """获取初始状态"""
        pass

    def create_workflow_id(self) -> str:
        """创建工作流ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{self.__class__.__name__.lower()}_{timestamp}"

    def run(
        self, initial_state: Dict[str, Any], config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """运行工作流"""
        if not self.graph:
            raise RuntimeError("工作流图未初始化")

        try:
            # 设置检查点配置
            run_config = {
                "configurable": {
                    "thread_id": initial_state.get("workflow_id", "default")
                }
            }
            if config:
                run_config.update(config)

            # 运行工作流
            result = self.graph.invoke(initial_state, config=run_config)

            logger.info(f"工作流 {initial_state.get('workflow_id')} 完成")
            return result

        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            return {
                **initial_state,
                "status": "failed",
                "error": str(e),
                "updated_at": datetime.now().isoformat(),
            }

    def stream_run(
        self, initial_state: Dict[str, Any], config: Optional[Dict[str, Any]] = None
    ):
        """流式运行工作流"""
        if not self.graph:
            raise RuntimeError("工作流图未初始化")

        run_config = {
            "configurable": {"thread_id": initial_state.get("workflow_id", "default")}
        }
        if config:
            run_config.update(config)

        for step in self.graph.stream(initial_state, config=run_config):
            yield step

    def get_state(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        if not self.checkpointer:
            return None

        try:
            config = {"configurable": {"thread_id": workflow_id}}
            state = self.graph.get_state(config)
            return state.values if state else None
        except Exception as e:
            logger.error(f"获取工作流状态失败: {str(e)}")
            return None

    def pause_workflow(self, workflow_id: str) -> bool:
        """暂停工作流"""
        # 这里可以实现暂停逻辑
        logger.info(f"暂停工作流: {workflow_id}")
        return True

    def resume_workflow(self, workflow_id: str) -> bool:
        """恢复工作流"""
        # 这里可以实现恢复逻辑
        logger.info(f"恢复工作流: {workflow_id}")
        return True

    def cancel_workflow(self, workflow_id: str) -> bool:
        """取消工作流"""
        # 这里可以实现取消逻辑
        logger.info(f"取消工作流: {workflow_id}")
        return True

    def validate_state(self, state: Dict[str, Any]) -> bool:
        """验证状态有效性"""
        required_fields = ["workflow_id", "step", "status"]
        return all(field in state for field in required_fields)

    def handle_error(self, state: Dict[str, Any], error: Exception) -> Dict[str, Any]:
        """处理错误"""
        error_msg = str(error)
        logger.error(f"工作流错误: {error_msg}")

        return {
            **state,
            "status": "failed",
            "error": error_msg,
            "updated_at": datetime.now().isoformat(),
        }

    def should_continue(self, state: Dict[str, Any]) -> str:
        """决定是否继续执行"""
        if state.get("status") == "failed":
            return END
        elif state.get("status") == "completed":
            return END
        else:
            return "continue"

    def format_llm_messages(
        self,
        system_prompt: str,
        user_content: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, str]]:
        """格式化LLM消息"""
        messages = [{"role": "system", "content": system_prompt}]

        if context:
            context_str = json.dumps(context, ensure_ascii=False, indent=2)
            user_content = f"上下文信息:\n{context_str}\n\n{user_content}"

        messages.append({"role": "user", "content": user_content})
        return messages

    def parse_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析JSON响应"""
        try:
            # 首先检查是否为纯JSON格式（快速路径）
            stripped_response = response.strip()
            if stripped_response.startswith("{") and stripped_response.endswith("}"):
                try:
                    return json.loads(stripped_response)
                except json.JSONDecodeError:
                    # 如果直接解析失败，继续下面的清理流程
                    pass

            # 如果不是纯JSON或直接解析失败，进行内容清理
            logger.debug(f"响应需要清理，长度: {len(response)} 字符")
            cleaned_response = self._clean_llm_response(response)

            if cleaned_response:
                try:
                    return json.loads(cleaned_response)
                except json.JSONDecodeError as e_inner:
                    logger.error(f"清理后的JSON仍然无法解析: {e_inner}")
                    logger.error(f"清理后内容: {cleaned_response[:200]}...")
                    return None

            logger.error("无法从响应中提取有效JSON")
            logger.error(f"原始响应前200字符: {response[:200]}...")
            return None

        except Exception as e:
            logger.error(f"解析JSON响应时发生未知错误: {e}")
            return None

    def _clean_llm_response(self, response: str) -> Optional[str]:
        """清理LLM响应，移除警告信息和其他非JSON内容"""
        import re

        # 方法1: 提取markdown代码块中的JSON
        match = re.search(r"```json\s*\n(.*?)\n```", response, re.DOTALL)
        if match:
            json_str = match.group(1).strip()
            logger.debug("从markdown代码块中提取JSON")
            return json_str

        # 方法2: 查找以{开始、以}结束的JSON对象
        # 使用更严格的模式来匹配完整的JSON对象
        json_pattern = r"(\{(?:[^{}]|{[^{}]*})*\})"
        matches = re.findall(json_pattern, response, re.DOTALL)

        for match in matches:
            # 尝试解析每个可能的JSON对象
            try:
                json.loads(match)
                logger.debug("找到有效的JSON对象")
                return match
            except json.JSONDecodeError:
                continue

        # 方法3: 移除已知的干扰信息模式
        cleaned = response

        # 移除LiteLLM的Provider List输出
        cleaned = re.sub(r"Provider List:.*?(?=\{)", "", cleaned, flags=re.DOTALL)

        # 移除Pydantic警告
        cleaned = re.sub(
            r".*?Pydantic serializer warnings:.*?(?=\{)", "", cleaned, flags=re.DOTALL
        )

        # 移除其他常见的警告模式
        cleaned = re.sub(r".*?UserWarning:.*?(?=\{)", "", cleaned, flags=re.DOTALL)

        # 移除Python路径相关的警告
        cleaned = re.sub(r".*?\.py:\d+:.*?(?=\{)", "", cleaned, flags=re.DOTALL)

        # 移除HTTP相关的输出
        cleaned = re.sub(r".*?https?://.*?(?=\{)", "", cleaned, flags=re.DOTALL)

        # 查找第一个完整的JSON对象
        match = re.search(r"(\{.*\})", cleaned, re.DOTALL)
        if match:
            json_str = match.group(1).strip()
            logger.debug("通过清理干扰信息后提取JSON")
            return json_str

        logger.warning("无法提取有效的JSON内容")
        return None
