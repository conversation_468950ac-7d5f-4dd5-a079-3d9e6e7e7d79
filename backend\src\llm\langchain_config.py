"""
LangChain LLM 配置模块

使用 LangChain 官方的 LLM 调用方案替换 LiteLLM
支持 Google Gemini 和 OpenRouter 两个提供商
"""

import os
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..utils.logging_config import get_logger

logger = get_logger(__name__)


class ModelProvider(Enum):
    """模型提供商枚举"""
    GOOGLE_GEMINI = "google_gemini"
    OPENROUTER = "openrouter"


class TaskType(Enum):
    """任务类型枚举"""
    CHARACTER_ANALYSIS = "character_analysis"
    DIALOGUE_ANNOTATION = "dialogue_annotation"


@dataclass
class LangChainModelConfig:
    """LangChain 模型配置
    
    字段说明：
    - name: 模型名称（用于 LangChain 调用）
    - display_name: 显示名称（用于日志和用户界面）
    - provider: 模型提供商
    - api_key_env: API 密钥环境变量名
    - base_url: API 基础 URL（OpenRouter 需要）
    - model_provider: LangChain 的 model_provider 参数（Google Gemini 需要）
    - max_output_tokens: 最大输出 token 数
    - context_length: 最大上下文长度
    - temperature: 温度参数
    - timeout: 超时时间（秒）
    - input_cost_per_1k_tokens: 输入成本（¥/千tokens）
    - output_cost_per_1k_tokens: 输出成本（¥/千tokens）
    """
    
    name: str
    display_name: str
    provider: ModelProvider
    api_key_env: str
    base_url: Optional[str] = None
    model_provider: Optional[str] = None
    max_output_tokens: int = 4000
    context_length: int = 128000
    temperature: float = 0.1
    timeout: int = 30
    input_cost_per_1k_tokens: float = 0.0
    output_cost_per_1k_tokens: float = 0.0

    def calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """计算调用成本"""
        input_cost = (input_tokens / 1000) * self.input_cost_per_1k_tokens
        output_cost = (output_tokens / 1000) * self.output_cost_per_1k_tokens
        return input_cost + output_cost


@dataclass
class TaskConfig:
    """任务配置"""
    primary_model: str
    fallback_models: List[str]
    budget_limit: float
    max_retries: int
    quality_threshold: float


class LangChainLLMConfig:
    """LangChain LLM 配置管理器"""

    def __init__(self):
        self.models = self._init_models()
        self.tasks = self._init_tasks()
        self.cost_limits = self._init_cost_limits()

    def _init_models(self) -> Dict[str, LangChainModelConfig]:
        """初始化模型配置"""
        return {
            # Google Gemini 模型
            "google-gemini-2.5-flash": LangChainModelConfig(
                name="gemini-2.0-flash",  # LangChain 中的实际模型名
                display_name="Google Gemini 2.5 Flash",
                provider=ModelProvider.GOOGLE_GEMINI,
                api_key_env="GOOGLE_API_KEY",
                model_provider="google_genai",  # LangChain 的 model_provider 参数
                max_output_tokens=8192,
                context_length=1_048_576,  # 1M context
                temperature=0.1,
                input_cost_per_1k_tokens=0.0,  # 免费模型
                output_cost_per_1k_tokens=0.0,  # 免费模型
            ),
            "google-gemini-2.5-flash-lite": LangChainModelConfig(
                name="gemini-2.0-flash",  # 使用相同的模型，通过参数控制
                display_name="Google Gemini 2.5 Flash Lite",
                provider=ModelProvider.GOOGLE_GEMINI,
                api_key_env="GOOGLE_API_KEY",
                model_provider="google_genai",
                max_output_tokens=4096,  # 更小的输出限制
                context_length=1_048_576,
                temperature=0.1,
                input_cost_per_1k_tokens=0.0,
                output_cost_per_1k_tokens=0.0,
            ),
            
            # OpenRouter 模型
            "openrouter-gemini-2.5-flash": LangChainModelConfig(
                name="google/gemini-2.5-flash",  # OpenRouter 的模型名称
                display_name="OpenRouter Gemini 2.5 Flash",
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                max_output_tokens=66_000,
                context_length=1_048_576,
                temperature=0.1,
                input_cost_per_1k_tokens=0.00108,  # $0.15/M tokens
                output_cost_per_1k_tokens=0.00432,  # $0.60/M tokens
            ),
            "openrouter-gemini-2.5-flash-lite": LangChainModelConfig(
                name="google/gemini-2.5-flash",  # 使用相同模型，通过参数控制
                display_name="OpenRouter Gemini 2.5 Flash Lite",
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                max_output_tokens=32_000,  # 更小的输出限制
                context_length=1_048_576,
                temperature=0.1,
                input_cost_per_1k_tokens=0.00108,
                output_cost_per_1k_tokens=0.00432,
            ),
        }

    def _init_tasks(self) -> Dict[TaskType, TaskConfig]:
        """初始化任务配置"""
        # 统一任务配置，优先使用免费的 Google Gemini
        default_task_config = TaskConfig(
            primary_model="google-gemini-2.5-flash",
            fallback_models=[
                "google-gemini-2.5-flash-lite",
                "openrouter-gemini-2.5-flash",
                "openrouter-gemini-2.5-flash-lite",
            ],
            budget_limit=10.0,
            max_retries=3,
            quality_threshold=0.8,
        )

        return {
            TaskType.CHARACTER_ANALYSIS: default_task_config,
            TaskType.DIALOGUE_ANNOTATION: default_task_config,
        }

    def _init_cost_limits(self) -> Dict[str, float]:
        """初始化成本限制"""
        return {
            "daily_budget": float(os.getenv("LLM_DAILY_BUDGET", "100.0")),
            "per_chapter_limit": float(os.getenv("LLM_CHAPTER_LIMIT", "5.0")),
            "emergency_threshold": float(os.getenv("LLM_EMERGENCY_THRESHOLD", "0.1")),
        }

    def get_model_config(self, model_name: str) -> Optional[LangChainModelConfig]:
        """获取模型配置"""
        return self.models.get(model_name)

    def get_task_config(self, task_type: TaskType) -> Optional[TaskConfig]:
        """获取任务配置"""
        return self.tasks.get(task_type)

    def get_models_by_provider(self, provider: ModelProvider) -> List[str]:
        """根据提供商获取模型列表"""
        return [
            name for name, config in self.models.items() 
            if config.provider == provider
        ]

    def list_available_models(self) -> List[str]:
        """列出所有可用模型"""
        return list(self.models.keys())

    def validate_config(self) -> bool:
        """验证配置"""
        try:
            # 检查必需的环境变量
            required_env_vars = set()
            for config in self.models.values():
                required_env_vars.add(config.api_key_env)
            
            missing_vars = []
            for var in required_env_vars:
                if not os.getenv(var):
                    missing_vars.append(var)
            
            if missing_vars:
                logger.warning(f"缺少环境变量: {missing_vars}")
                return False
            
            # 检查任务配置
            for task_type, task_config in self.tasks.items():
                if task_config.primary_model not in self.models:
                    logger.error(f"任务 {task_type.value} 的主模型 {task_config.primary_model} 不存在")
                    return False
                
                for fallback_model in task_config.fallback_models:
                    if fallback_model not in self.models:
                        logger.warning(f"任务 {task_type.value} 的备用模型 {fallback_model} 不存在")
            
            logger.info("LangChain LLM 配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False


# 全局配置实例
llm_config = LangChainLLMConfig()
