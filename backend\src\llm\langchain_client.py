"""
LangChain LLM 客户端

使用 LangChain 官方的 LLM 调用方案替换 LiteLLM
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from langchain.chat_models import init_chat_model
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .langchain_config import LangChainLLMConfig, LangChainModelConfig, ModelProvider, TaskType
from .cost_tracker import CostTracker
from ..utils.logging_config import get_logger

logger = get_logger(__name__)


class LangChainLLMClient:
    """LangChain LLM 客户端"""

    def __init__(self, config: Optional[LangChainLLMConfig] = None):
        self.config = config or LangChainLLMConfig()
        self.cost_tracker = CostTracker()
        self._model_cache: Dict[str, BaseChatModel] = {}
        
        logger.info("LangChain LLM 客户端初始化完成")

    def _get_or_create_model(self, model_name: str) -> BaseChatModel:
        """获取或创建模型实例（带缓存）"""
        if model_name in self._model_cache:
            return self._model_cache[model_name]
        
        model_config = self.config.get_model_config(model_name)
        if not model_config:
            raise ValueError(f"未找到模型配置: {model_name}")
        
        # 获取 API 密钥
        api_key = os.getenv(model_config.api_key_env)
        if not api_key:
            raise ValueError(f"未找到API密钥环境变量: {model_config.api_key_env}")
        
        try:
            if model_config.provider == ModelProvider.GOOGLE_GEMINI:
                # 使用 LangChain 的 init_chat_model 创建 Google Gemini 模型
                model = init_chat_model(
                    model_config.name,
                    model_provider=model_config.model_provider,
                    api_key=api_key,
                    temperature=model_config.temperature,
                    max_tokens=model_config.max_output_tokens,
                    timeout=model_config.timeout,
                )
                
            elif model_config.provider == ModelProvider.OPENROUTER:
                # 使用 ChatOpenAI 创建 OpenRouter 模型
                model = ChatOpenAI(
                    model=model_config.name,
                    api_key=api_key,
                    base_url=model_config.base_url,
                    temperature=model_config.temperature,
                    max_tokens=model_config.max_output_tokens,
                    timeout=model_config.timeout,
                )
                
            else:
                raise ValueError(f"不支持的模型提供商: {model_config.provider}")
            
            # 缓存模型实例
            self._model_cache[model_name] = model
            logger.info(f"成功创建模型实例: {model_name} ({model_config.provider.value})")
            return model
            
        except Exception as e:
            logger.error(f"创建模型实例失败 {model_name}: {e}")
            raise

    def _convert_messages(self, messages: List[Dict[str, str]]) -> List[Union[HumanMessage, SystemMessage, AIMessage]]:
        """将消息格式转换为 LangChain 格式"""
        langchain_messages = []
        
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "system":
                langchain_messages.append(SystemMessage(content=content))
            elif role == "user":
                langchain_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                langchain_messages.append(AIMessage(content=content))
            else:
                # 默认作为用户消息处理
                langchain_messages.append(HumanMessage(content=content))
        
        return langchain_messages

    def _extract_usage_info(self, response: Any) -> Dict[str, int]:
        """从响应中提取使用信息"""
        usage = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
        
        try:
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage_metadata = response.usage_metadata
                usage["prompt_tokens"] = getattr(usage_metadata, 'input_tokens', 0)
                usage["completion_tokens"] = getattr(usage_metadata, 'output_tokens', 0)
                usage["total_tokens"] = getattr(usage_metadata, 'total_tokens', 0)
            elif hasattr(response, 'response_metadata') and response.response_metadata:
                # 某些模型可能在 response_metadata 中包含使用信息
                metadata = response.response_metadata
                if 'token_usage' in metadata:
                    token_usage = metadata['token_usage']
                    usage["prompt_tokens"] = token_usage.get('prompt_tokens', 0)
                    usage["completion_tokens"] = token_usage.get('completion_tokens', 0)
                    usage["total_tokens"] = token_usage.get('total_tokens', 0)
        except Exception as e:
            logger.debug(f"提取使用信息失败: {e}")
        
        return usage

    def call_model(
        self, model_name: str, messages: List[Dict[str, str]], **kwargs
    ) -> Dict[str, Any]:
        """调用指定模型"""
        try:
            model_config = self.config.get_model_config(model_name)
            if not model_config:
                raise ValueError(f"未找到模型配置: {model_name}")
            
            # 获取模型实例
            model = self._get_or_create_model(model_name)
            
            # 转换消息格式
            langchain_messages = self._convert_messages(messages)
            
            # 调用模型
            logger.debug(f"调用模型 {model_name}，消息数量: {len(langchain_messages)}")
            response = model.invoke(langchain_messages)
            
            # 提取使用信息
            usage = self._extract_usage_info(response)
            
            # 记录成本
            total_cost = 0
            if usage["prompt_tokens"] > 0 or usage["completion_tokens"] > 0:
                self.cost_tracker.add_cost(
                    model_name,
                    usage["prompt_tokens"],
                    usage["completion_tokens"],
                    model_config.input_cost_per_1k_tokens,
                    model_config.output_cost_per_1k_tokens,
                )
                total_cost = model_config.calculate_cost(
                    usage["prompt_tokens"], usage["completion_tokens"]
                )
            
            # 返回标准化响应
            return {
                "success": True,
                "content": response.content,
                "model": model_name,
                "usage": usage,
                "cost": total_cost,
            }
            
        except Exception as e:
            logger.error(f"模型 {model_name} 调用失败: {str(e)}")
            return {"success": False, "error": str(e), "model": model_name}

    def call_with_fallback(
        self, task_type: TaskType, messages: List[Dict[str, str]], **kwargs
    ) -> Dict[str, Any]:
        """带故障转移的模型调用"""
        task_config = self.config.get_task_config(task_type)
        if not task_config:
            raise ValueError(f"未找到任务配置: {task_type}")

        # 检查预算
        if self.cost_tracker.get_daily_cost() > self.config.cost_limits["daily_budget"]:
            logger.warning("已超出日预算限制")
            return {"success": False, "error": "已超出日预算限制"}

        # 尝试主模型和备用模型
        models_to_try = [task_config.primary_model] + task_config.fallback_models

        for model_name in models_to_try:
            logger.info(f"尝试使用模型: {model_name}")

            result = self.call_model(model_name, messages, **kwargs)

            if result["success"]:
                logger.info(f"模型 {model_name} 调用成功")
                return result
            else:
                logger.warning(f"模型 {model_name} 调用失败: {result.get('error')}")
                continue

        # 所有模型都失败
        return {
            "success": False,
            "error": "所有模型调用都失败",
            "tried_models": models_to_try,
        }

    def test_connection(self, model_name: str) -> bool:
        """测试模型连接"""
        try:
            test_messages = [{"role": "user", "content": "Hello"}]
            result = self.call_model(model_name, test_messages)
            return result["success"]
        except Exception as e:
            logger.error(f"测试模型 {model_name} 连接失败: {e}")
            return False

    def list_available_models(self) -> List[str]:
        """列出可用模型"""
        return self.config.list_available_models()

    def get_cost_summary(self) -> Dict[str, Any]:
        """获取成本摘要"""
        return self.cost_tracker.get_summary()

    def validate_response(self, response: str, expected_format: str = "json") -> bool:
        """验证响应格式"""
        if expected_format == "json":
            try:
                json.loads(response)
                return True
            except json.JSONDecodeError:
                return False

        # 其他格式验证可以在这里添加
        return True
