{"[python]": {"editor.formatOnSave": true, "editor.defaultFormatter": "charliermarsh.ruff", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}}, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[json]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": "./frontend/.prettierrc", "eslint.workingDirectories": ["frontend"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "python.testing.pytestArgs": ["backend"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "editorjumper.selectedIDE": "PyCharm", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, ".yoyo": true, "frontend/node_modules": true, "frontend/dist": true, ".idea": true, ".pytest_cache": true}}