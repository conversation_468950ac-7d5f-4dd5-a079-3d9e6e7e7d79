"""角色分析模块

包含角色和章节分析相关功能
"""

import json
from datetime import datetime
from typing import Dict, List

from ...utils.logging_config import get_logger
from ..config import TaskType

logger = get_logger(__name__)

from .langsmith_config import log_workflow_metrics, with_langsmith_tracing  # noqa: E402
from .novel_prompts import NovelProcessingPrompts  # noqa: E402


class CharacterAnalyzer:
    """角色分析器"""

    def __init__(self, workflow_instance):
        """
        初始化角色分析器

        Args:
            workflow_instance: 主工作流实例，用于访问共享方法
        """
        self.workflow = workflow_instance

    @property
    def llm_client(self):
        """延迟访问llm_client"""
        return self.workflow.llm_client

    @with_langsmith_tracing(
        run_name="analyze_characters_and_chapters",
        tags=["character_analysis", "llm_call"],
        exclude_fields=[],  # 不排除字段，因为函数内部需要使用这些字段
    )
    def analyze_characters(self, state):
        """分析角色和章节 - 完整的小说分析"""
        logger.info(f"{state['workflow_id']}: 开始完整的小说分析（角色+章节）")

        # 调试：打印状态中的关键字段
        logger.debug(f"{state['workflow_id']}: 状态中的字段: {list(state.keys())}")
        logger.debug(f"{state['workflow_id']}: chapter_contents存在: {'chapter_contents' in state}")
        if 'chapter_contents' in state:
            logger.debug(f"{state['workflow_id']}: chapter_contents长度: {len(state['chapter_contents'])}")

        # 检查chapter_contents是否存在
        if "chapter_contents" not in state:
            logger.error(f"{state['workflow_id']}: 状态中缺少chapter_contents字段")
            logger.error(f"{state['workflow_id']}: 当前状态字段: {list(state.keys())}")
            return self.workflow.handle_error(state, Exception("状态中缺少chapter_contents字段，请确保章节分割步骤已完成"))

        chapter_contents = state["chapter_contents"]
        if not chapter_contents:
            logger.error(f"{state['workflow_id']}: chapter_contents为空")
            return self.workflow.handle_error(state, Exception("章节内容为空，请检查章节分割步骤"))

        # 记录分析开始指标
        log_workflow_metrics(
            state,
            "analyze_characters",
            {
                "total_chapters": len(chapter_contents),
                "avg_chapter_length": sum(len(ch) for ch in chapter_contents)
                // len(chapter_contents)
                if chapter_contents
                else 0,
            },
        )

        try:
            # 使用已验证的章节内容
            logger.info(
                f"{state['workflow_id']}: 使用已分割的 {len(chapter_contents)} 个章节"
            )

            # 准备完整的章节内容供分析
            chapters_text = ""
            for i, chapter_content in enumerate(chapter_contents, 1):
                chapters_text += f"\n\n=== 第{i}章 ===\n{chapter_content}"

            # 使用新的提示词模板
            messages = NovelProcessingPrompts.format_complete_analysis_messages(
                novel_title=state["novel_title"],
                novel_content=state["novel_content"],
                chapter_count=len(chapter_contents),
                chapters_text=chapters_text,
            )

            # 调用LLM
            result = self.llm_client.call_with_fallback(
                TaskType.CHARACTER_ANALYSIS, messages
            )

            if not result["success"]:
                raise Exception(f"小说分析失败: {result.get('error')}")

            # 解析和验证 JSON 格式的分析结果
            try:
                # 尝试解析 JSON
                analysis_result = json.loads(result["content"].strip())

                # 验证顶级结构
                if not isinstance(analysis_result, dict):
                    raise ValueError("分析结果必须是对象格式")

                # 验证必需字段
                required_top_fields = ["characters", "chapters", "overall_analysis"]
                for field in required_top_fields:
                    if field not in analysis_result:
                        raise ValueError(f"分析结果缺少必需字段: {field}")

                # 验证 characters 字段
                characters = analysis_result.get("characters", [])
                if not isinstance(characters, list):
                    raise ValueError("characters 字段必须是数组格式")

                # 验证 chapters 字段
                chapters_analysis = analysis_result.get("chapters", [])
                if not isinstance(chapters_analysis, list):
                    raise ValueError("chapters 字段必须是数组格式")

                # 验证 overall_analysis 字段
                overall_analysis = analysis_result.get("overall_analysis", {})
                if not isinstance(overall_analysis, dict):
                    raise ValueError("overall_analysis 字段必须是对象格式")

                logger.info(
                    f"{state['workflow_id']}: 分析完成：{len(characters)} 个角色，{len(chapters_analysis)} 个章节"
                )

                analysis_status = "success"

            except json.JSONDecodeError as e:
                logger.error(f"{state['workflow_id']}: JSON 解析失败: {e}")
                # 尝试降级处理：提取基本信息
                logger.info(f"{state['workflow_id']}: 尝试降级处理")

                characters = []
                chapters_analysis = []
                overall_analysis = {"error": "JSON解析失败，使用降级处理"}
                analysis_status = "failed"

            except ValueError as e:
                logger.error(f"{state['workflow_id']}: 分析格式验证失败: {e}")
                # 尝试部分解析
                characters = analysis_result.get("characters", [])
                chapters_analysis = analysis_result.get("chapters", [])
                overall_analysis = analysis_result.get("overall_analysis", {})
                analysis_status = "success_fallback"

            return {
                **state,
                "step": "annotate_dialogues",
                "characters": characters,
                "chapters": chapters_analysis,
                "overall_analysis": overall_analysis,
                "total_chapters": len(chapter_contents),
                "analysis_status": analysis_status,  # 分析解析状态
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 0.4,
            }

        except Exception as e:
            return self.workflow.handle_error(state, e)

    def analyze_dialogue_characteristics(self, characters: List[Dict]) -> str:
        """分析对话特征"""
        if not characters:
            return "缺乏对话分析数据"

        speech_styles = [char.get("speech_style", "") for char in characters]
        if any("幽默" in style for style in speech_styles):
            return "对话风格轻松幽默"
        elif any("正式" in style for style in speech_styles):
            return "对话风格正式严肃"
        else:
            return "对话风格自然朴实"
